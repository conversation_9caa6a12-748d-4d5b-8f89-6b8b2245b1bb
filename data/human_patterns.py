"""
Simulated human behavioral patterns for scalar implicature experiments.
Based on established findings from psycholinguistic research.
"""

import numpy as np
import random
from typing import Dict, List, Tuple, Any
from dataclasses import dataclass
from .scenarios import ScalarScenario, ScaleType, ContextType, NegationType

@dataclass
class HumanResponse:
    """Represents a simulated human response to a scalar implicature scenario."""
    scenario_id: str
    participant_id: str
    implicature_derived: bool
    confidence: float  # 0-1 scale
    response_time: float  # simulated response time in seconds
    explanation: str  # simulated explanation

class HumanPatternSimulator:
    """Simulates human behavioral patterns in scalar implicature tasks."""
    
    def __init__(self, base_rates: Dict[str, float] = None, individual_variation: float = 0.2):
        """
        Initialize the human pattern simulator.
        
        Args:
            base_rates: Base implicature rates for different conditions
            individual_variation: Standard deviation for individual differences
        """
        self.base_rates = base_rates or {
            "quantifier_simple": 0.85,
            "quantifier_embedded": 0.70,
            "quantifier_question": 0.75,
            "quantifier_conditional": 0.65,
            "adjectival_simple": 0.65,
            "adjectival_embedded": 0.50,
            "adjectival_question": 0.55,
            "adjectival_conditional": 0.45,
            "negation_penalty": 0.15,
            "context_penalty": 0.10
        }
        self.individual_variation = individual_variation
        self.participants = {}  # Store individual participant characteristics
        
    def _get_participant_bias(self, participant_id: str) -> float:
        """Get or create individual participant bias."""
        if participant_id not in self.participants:
            # Create individual bias (normally distributed around 0)
            bias = np.random.normal(0, self.individual_variation)
            self.participants[participant_id] = {
                'bias': bias,
                'consistency': np.random.uniform(0.7, 0.95)  # How consistent they are
            }
        return self.participants[participant_id]['bias']
    
    def _get_participant_consistency(self, participant_id: str) -> float:
        """Get participant consistency factor."""
        return self.participants[participant_id]['consistency']
    
    def _calculate_base_rate(self, scenario: ScalarScenario) -> float:
        """Calculate base implicature rate for a scenario."""
        # Start with scale type base rate
        if scenario.scale.scale_type == ScaleType.QUANTIFIER:
            base_rate = self.base_rates[f"quantifier_{scenario.context_type.value}"]
        else:
            base_rate = self.base_rates[f"adjectival_{scenario.context_type.value}"]
        
        # Apply negation penalty
        if scenario.negation_type != NegationType.NONE:
            base_rate -= self.base_rates["negation_penalty"]
        
        # Apply context-specific adjustments
        if scenario.context_type in [ContextType.EMBEDDED, ContextType.CONDITIONAL]:
            base_rate -= self.base_rates["context_penalty"]
        
        # Ensure rate is within bounds
        return max(0.1, min(0.9, base_rate))
    
    def _simulate_response_time(self, implicature_derived: bool, scenario: ScalarScenario) -> float:
        """Simulate response time based on processing complexity."""
        # Base response time
        base_time = 2.0  # seconds
        
        # Implicature derivation adds processing time
        if implicature_derived:
            base_time += 0.5
        
        # Complex contexts add time
        if scenario.context_type in [ContextType.EMBEDDED, ContextType.CONDITIONAL]:
            base_time += 0.3
        
        # Negation adds processing time
        if scenario.negation_type != NegationType.NONE:
            base_time += 0.4
        
        # Add individual variation
        response_time = np.random.gamma(base_time, 0.5)
        return max(0.5, response_time)
    
    def _generate_explanation(self, implicature_derived: bool, scenario: ScalarScenario) -> str:
        """Generate a simulated explanation for the response."""
        if implicature_derived:
            if scenario.scale.scale_type == ScaleType.QUANTIFIER:
                return f"If it were {scenario.scale.strong_term}, they would have said {scenario.scale.strong_term}"
            else:
                return f"If it were {scenario.scale.strong_term}, they would have said {scenario.scale.strong_term}"
        else:
            return "The statement could be true even if the stronger alternative is also true"
    
    def simulate_response(self, scenario: ScalarScenario, participant_id: str) -> HumanResponse:
        """Simulate a human response to a scalar implicature scenario."""
        
        # Calculate probability of deriving implicature
        base_rate = self._calculate_base_rate(scenario)
        participant_bias = self._get_participant_bias(participant_id)
        consistency = self._get_participant_consistency(participant_id)
        
        # Adjust rate with individual bias
        adjusted_rate = base_rate + participant_bias
        adjusted_rate = max(0.05, min(0.95, adjusted_rate))
        
        # Determine if implicature is derived
        implicature_derived = random.random() < adjusted_rate
        
        # Add consistency noise
        if random.random() > consistency:
            implicature_derived = not implicature_derived
        
        # Calculate confidence (higher for more typical responses)
        if implicature_derived:
            confidence = np.random.beta(3, 1)  # Skewed toward high confidence
        else:
            confidence = np.random.beta(2, 2)  # More uniform
        
        # Simulate response time
        response_time = self._simulate_response_time(implicature_derived, scenario)
        
        # Generate explanation
        explanation = self._generate_explanation(implicature_derived, scenario)
        
        return HumanResponse(
            scenario_id=scenario.scenario_id,
            participant_id=participant_id,
            implicature_derived=implicature_derived,
            confidence=confidence,
            response_time=response_time,
            explanation=explanation
        )
    
    def simulate_experiment(self, scenarios: List[ScalarScenario], 
                          num_participants: int = 50) -> List[HumanResponse]:
        """Simulate a complete experiment with multiple participants."""
        responses = []
        
        for participant_id in range(num_participants):
            participant_name = f"P{participant_id:03d}"
            
            for scenario in scenarios:
                response = self.simulate_response(scenario, participant_name)
                responses.append(response)
        
        return responses
    
    def get_summary_statistics(self, responses: List[HumanResponse]) -> Dict[str, Any]:
        """Calculate summary statistics from simulated responses."""
        if not responses:
            return {}
        
        # Overall implicature rate
        total_implicatures = sum(1 for r in responses if r.implicature_derived)
        overall_rate = total_implicatures / len(responses)
        
        # Average confidence
        avg_confidence = np.mean([r.confidence for r in responses])
        
        # Average response time
        avg_response_time = np.mean([r.response_time for r in responses])
        
        # Group by conditions for detailed analysis
        by_scale_type = {}
        by_context = {}
        by_negation = {}
        
        # This would require access to the original scenarios
        # For now, return basic statistics
        
        return {
            'overall_implicature_rate': overall_rate,
            'average_confidence': avg_confidence,
            'average_response_time': avg_response_time,
            'total_responses': len(responses),
            'num_participants': len(set(r.participant_id for r in responses))
        }
    
    def export_responses(self, responses: List[HumanResponse], filename: str = None) -> Dict[str, Any]:
        """Export responses in a format suitable for analysis."""
        data = {
            'responses': [
                {
                    'scenario_id': r.scenario_id,
                    'participant_id': r.participant_id,
                    'implicature_derived': r.implicature_derived,
                    'confidence': r.confidence,
                    'response_time': r.response_time,
                    'explanation': r.explanation
                }
                for r in responses
            ],
            'summary': self.get_summary_statistics(responses)
        }
        
        if filename:
            import json
            with open(filename, 'w') as f:
                json.dump(data, f, indent=2)
        
        return data
