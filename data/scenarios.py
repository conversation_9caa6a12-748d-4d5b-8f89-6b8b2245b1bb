"""
Scalar implicature scenario generation for experiments.
"""

import random
from typing import List, Dict, Tu<PERSON>, Any
from dataclasses import dataclass
from enum import Enum

class ScaleType(Enum):
    """Types of scalar scales."""
    QUANTIFIER = "quantifier"
    ADJECTIVAL = "adjectival"

class ContextType(Enum):
    """Types of linguistic contexts."""
    SIMPLE = "simple"
    EMBEDDED = "embedded"
    QUESTION = "question"
    CONDITIONAL = "conditional"

class NegationType(Enum):
    """Types of negation."""
    NONE = "none"
    SENTENTIAL = "sentential"  # "It's not the case that..."
    CONSTITUENT = "constituent"  # "The coffee is not warm"

@dataclass
class ScalarScale:
    """Represents a scalar scale with weak and strong terms."""
    weak_term: str
    strong_term: str
    scale_type: ScaleType
    domain: str  # e.g., "temperature", "quantity", "size"
    
class ScalarScenario:
    """Represents a scalar implicature test scenario."""
    
    def __init__(self, scale: ScalarScale, context_type: ContextType, 
                 negation_type: NegationType, scenario_text: str, 
                 target_sentence: str, expected_implicature: str,
                 scenario_id: str = None):
        self.scale = scale
        self.context_type = context_type
        self.negation_type = negation_type
        self.scenario_text = scenario_text
        self.target_sentence = target_sentence
        self.expected_implicature = expected_implicature
        self.scenario_id = scenario_id or f"{scale.weak_term}_{scale.strong_term}_{context_type.value}_{negation_type.value}"

class ScenarioGenerator:
    """Generates scalar implicature test scenarios."""
    
    def __init__(self):
        self.scales = self._initialize_scales()
        self.sentence_templates = self._initialize_templates()
        
    def _initialize_scales(self) -> List[ScalarScale]:
        """Initialize the scalar scales for testing."""
        scales = []
        
        # Quantifier scales
        scales.extend([
            ScalarScale("some", "all", ScaleType.QUANTIFIER, "quantity"),
            ScalarScale("many", "most", ScaleType.QUANTIFIER, "quantity"),
            ScalarScale("few", "none", ScaleType.QUANTIFIER, "quantity"),
            ScalarScale("several", "all", ScaleType.QUANTIFIER, "quantity"),
        ])
        
        # Adjectival scales
        scales.extend([
            ScalarScale("warm", "hot", ScaleType.ADJECTIVAL, "temperature"),
            ScalarScale("cool", "cold", ScaleType.ADJECTIVAL, "temperature"),
            ScalarScale("large", "huge", ScaleType.ADJECTIVAL, "size"),
            ScalarScale("small", "tiny", ScaleType.ADJECTIVAL, "size"),
            ScalarScale("good", "excellent", ScaleType.ADJECTIVAL, "quality"),
            ScalarScale("bad", "terrible", ScaleType.ADJECTIVAL, "quality"),
            ScalarScale("fast", "very fast", ScaleType.ADJECTIVAL, "speed"),
            ScalarScale("slow", "very slow", ScaleType.ADJECTIVAL, "speed"),
        ])
        
        return scales
    
    def _initialize_templates(self) -> Dict[str, Dict[str, List[str]]]:
        """Initialize sentence templates for different contexts."""
        return {
            "quantifier": {
                "simple": [
                    "{weak_term} of the students passed the exam.",
                    "{weak_term} people attended the meeting.",
                    "{weak_term} books on the shelf are interesting.",
                    "{weak_term} of the participants completed the survey."
                ],
                "embedded": [
                    "I think {weak_term} of the students passed the exam.",
                    "She believes {weak_term} people attended the meeting.",
                    "It seems that {weak_term} books are interesting.",
                    "The teacher said {weak_term} participants completed the survey."
                ],
                "question": [
                    "Did {weak_term} of the students pass the exam?",
                    "Were {weak_term} people at the meeting?",
                    "Are {weak_term} books on the shelf interesting?",
                    "Have {weak_term} participants completed the survey?"
                ],
                "conditional": [
                    "If {weak_term} students passed, the class was successful.",
                    "When {weak_term} people attend, we can start the meeting.",
                    "Since {weak_term} books are interesting, I'll read more.",
                    "Given that {weak_term} participants completed it, the survey is valid."
                ]
            },
            "adjectival": {
                "simple": [
                    "The coffee is {weak_term}.",
                    "The weather today is {weak_term}.",
                    "This room is {weak_term}.",
                    "The movie was {weak_term}."
                ],
                "embedded": [
                    "I think the coffee is {weak_term}.",
                    "She said the weather is {weak_term}.",
                    "It seems this room is {weak_term}.",
                    "He believes the movie was {weak_term}."
                ],
                "question": [
                    "Is the coffee {weak_term}?",
                    "Is the weather {weak_term} today?",
                    "Is this room {weak_term}?",
                    "Was the movie {weak_term}?"
                ],
                "conditional": [
                    "If the coffee is {weak_term}, I'll drink it.",
                    "When the weather is {weak_term}, we go outside.",
                    "Since the room is {weak_term}, we'll stay here.",
                    "Given the movie was {weak_term}, I enjoyed it."
                ]
            }
        }
    
    def generate_scenario(self, scale: ScalarScale, context_type: ContextType, 
                         negation_type: NegationType) -> ScalarScenario:
        """Generate a single scalar implicature scenario."""
        
        # Select appropriate template
        scale_key = scale.scale_type.value
        context_key = context_type.value
        
        templates = self.sentence_templates[scale_key][context_key]
        template = random.choice(templates)
        
        # Generate target sentence
        target_sentence = template.format(weak_term=scale.weak_term)
        
        # Apply negation if specified
        if negation_type == NegationType.SENTENTIAL:
            target_sentence = f"It's not the case that {target_sentence.lower()}"
        elif negation_type == NegationType.CONSTITUENT:
            if scale.scale_type == ScaleType.QUANTIFIER:
                # For quantifiers, negate the verb or use "not many/few"
                if "some" in target_sentence:
                    target_sentence = target_sentence.replace("some", "not all")
                elif "many" in target_sentence:
                    target_sentence = target_sentence.replace("many", "not many")
            else:
                # For adjectives, add "not" before the adjective
                target_sentence = target_sentence.replace(scale.weak_term, f"not {scale.weak_term}")
        
        # Generate expected implicature
        if negation_type == NegationType.NONE:
            expected_implicature = f"not {scale.strong_term}"
        else:
            # Negation contexts have more complex implicature patterns
            expected_implicature = f"possibly {scale.strong_term}"
        
        # Create scenario context
        scenario_text = f"Consider the following statement: '{target_sentence}'"
        
        return ScalarScenario(
            scale=scale,
            context_type=context_type,
            negation_type=negation_type,
            scenario_text=scenario_text,
            target_sentence=target_sentence,
            expected_implicature=expected_implicature
        )
    
    def generate_scenario_set(self, num_scenarios: int = 100) -> List[ScalarScenario]:
        """Generate a balanced set of scalar implicature scenarios."""
        scenarios = []
        
        # Calculate scenarios per condition
        num_scales = len(self.scales)
        num_contexts = len(ContextType)
        num_negations = len(NegationType)
        total_conditions = num_scales * num_contexts * num_negations
        
        scenarios_per_condition = max(1, num_scenarios // total_conditions)
        
        # Generate scenarios for each condition
        for scale in self.scales:
            for context_type in ContextType:
                for negation_type in NegationType:
                    for _ in range(scenarios_per_condition):
                        scenario = self.generate_scenario(scale, context_type, negation_type)
                        scenarios.append(scenario)
        
        # Shuffle scenarios
        random.shuffle(scenarios)
        
        # Trim to exact number if needed
        return scenarios[:num_scenarios]
    
    def get_scales_by_type(self, scale_type: ScaleType) -> List[ScalarScale]:
        """Get all scales of a specific type."""
        return [scale for scale in self.scales if scale.scale_type == scale_type]
    
    def get_scales_by_domain(self, domain: str) -> List[ScalarScale]:
        """Get all scales in a specific domain."""
        return [scale for scale in self.scales if scale.domain == domain]
