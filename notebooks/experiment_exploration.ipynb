{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Scalar Implicature Experiment Exploration\n", "\n", "This notebook provides interactive exploration of scalar implicature experiment results.\n", "\n", "## Setup"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import sys\n", "import os\n", "sys.path.append('..')\n", "\n", "import pandas as pd\n", "import numpy as np\n", "import matplotlib.pyplot as plt\n", "import seaborn as sns\n", "import json\n", "from typing import Dict, List, Any\n", "\n", "from src.analysis import ExperimentAnalyzer\n", "from src.experiment_runner import ExperimentRunner\n", "from config.experiment_config import get_experiment_config\n", "\n", "# Set up plotting\n", "plt.style.use('default')\n", "sns.set_palette(\"husl\")\n", "%matplotlib inline"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Load Experiment Results\n", "\n", "Load results from a completed experiment:"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Specify the path to your results file\n", "results_file = \"../results/comprehensive/experiment_results_latest.json\"\n", "\n", "# Check if file exists, if not use a sample path\n", "if not os.path.exists(results_file):\n", "    # Look for any results file in the results directory\n", "    results_dir = \"../results\"\n", "    if os.path.exists(results_dir):\n", "        for subdir in os.listdir(results_dir):\n", "            subdir_path = os.path.join(results_dir, subdir)\n", "            if os.path.isdir(subdir_path):\n", "                for file in os.listdir(subdir_path):\n", "                    if file.startswith(\"experiment_results_\") and file.endswith(\".json\"):\n", "                        results_file = os.path.join(subdir_path, file)\n", "                        break\n", "                if os.path.exists(results_file):\n", "                    break\n", "\n", "print(f\"Loading results from: {results_file}\")\n", "\n", "if os.path.exists(results_file):\n", "    analyzer = ExperimentAnalyzer(results_file)\n", "    print(\"Results loaded successfully!\")\n", "else:\n", "    print(\"No results file found. Please run an experiment first.\")\n", "    analyzer = None"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Quick Overview"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["if analyzer:\n", "    summary = analyzer.get_summary_statistics()\n", "    \n", "    print(\"EXPERIMENT SUMMARY:\")\n", "    print(\"=\" * 30)\n", "    for key, value in summary.items():\n", "        print(f\"{key}: {value}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Data Exploration"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["if analyzer and not analyzer.model_df.empty:\n", "    print(\"Model DataFrame shape:\", analyzer.model_df.shape)\n", "    print(\"\\nColumns:\", list(analyzer.model_df.columns))\n", "    print(\"\\nFirst few rows:\")\n", "    display(analyzer.model_df.head())"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["if analyzer and not analyzer.human_df.empty:\n", "    print(\"Human DataFrame shape:\", analyzer.human_df.shape)\n", "    print(\"\\nFirst few rows:\")\n", "    display(analyzer.human_df.head())"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Visualizations"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["if analyzer and not analyzer.model_df.empty:\n", "    # Overall implicature rates\n", "    fig, ax = plt.subplots(figsize=(10, 6))\n", "    \n", "    model_rate = analyzer.model_df['implicature_derived'].mean()\n", "    rates = [model_rate]\n", "    labels = ['Model']\n", "    \n", "    if not analyzer.human_df.empty:\n", "        human_rate = analyzer.human_df['implicature_derived'].mean()\n", "        rates.append(human_rate)\n", "        labels.append('Human')\n", "    \n", "    bars = ax.bar(labels, rates, alpha=0.7)\n", "    ax.set_ylabel('Implicature Derivation Rate')\n", "    ax.set_title('Overall Implicature Derivation Rates')\n", "    ax.set_ylim(0, 1)\n", "    \n", "    # Add value labels\n", "    for bar, rate in zip(bars, rates):\n", "        ax.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 0.01,\n", "               f'{rate:.3f}', ha='center', va='bottom')\n", "    \n", "    plt.show()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["if analyzer and not analyzer.model_df.empty and 'scale_type' in analyzer.model_df.columns:\n", "    # Rates by scale type\n", "    fig, ax = plt.subplots(figsize=(12, 6))\n", "    \n", "    model_by_scale = analyzer.model_df.groupby('scale_type')['implicature_derived'].mean()\n", "    x_pos = np.arange(len(model_by_scale))\n", "    \n", "    bars1 = ax.bar(x_pos - 0.2, model_by_scale.values, 0.4, \n", "                  label='Model', alpha=0.7)\n", "    \n", "    if not analyzer.human_df.empty and 'scale_type' in analyzer.human_df.columns:\n", "        human_by_scale = analyzer.human_df.groupby('scale_type')['implicature_derived'].mean()\n", "        bars2 = ax.bar(x_pos + 0.2, human_by_scale.values, 0.4, \n", "                      label='Human', alpha=0.7)\n", "    \n", "    ax.set_xlabel('Scale Type')\n", "    ax.set_ylabel('Implicature Derivation Rate')\n", "    ax.set_title('Implicature Rates by Scale Type')\n", "    ax.set_xticks(x_pos)\n", "    ax.set_xticklabels(model_by_scale.index)\n", "    ax.set_ylim(0, 1)\n", "    ax.legend()\n", "    \n", "    plt.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Interactive Analysis"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["if analyzer:\n", "    # Condition analysis\n", "    condition_analyses = analyzer.analyze_by_condition()\n", "    \n", "    print(\"ANALYSIS BY CONDITIONS:\")\n", "    print(\"=\" * 30)\n", "    \n", "    for condition, analysis in condition_analyses.items():\n", "        print(f\"\\n{condition.upper()}:\")\n", "        display(analysis)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["if analyzer and not analyzer.human_df.empty:\n", "    # Model-human comparison\n", "    comparison = analyzer.compare_model_human()\n", "    \n", "    print(\"MODEL-HUMAN COMPARISON:\")\n", "    print(\"=\" * 30)\n", "    \n", "    for key, value in comparison.items():\n", "        print(f\"\\n{key.upper()}:\")\n", "        if isinstance(value, dict):\n", "            for subkey, subvalue in value.items():\n", "                print(f\"  {subkey}: {subvalue}\")\n", "        else:\n", "            print(f\"  {value}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Custom Analysis\n", "\n", "Add your own analysis here:"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Example: Analyze specific scenarios\n", "if analyzer and not analyzer.model_df.empty:\n", "    # Filter for specific conditions\n", "    quantifier_simple = analyzer.model_df[\n", "        (analyzer.model_df['scale_type'] == 'quantifier') & \n", "        (analyzer.model_df['context_type'] == 'simple')\n", "    ]\n", "    \n", "    if not quantifier_simple.empty:\n", "        print(f\"Quantifier scales in simple context: {len(quantifier_simple)} scenarios\")\n", "        print(f\"Implicature rate: {quantifier_simple['implicature_derived'].mean():.3f}\")\n", "        print(f\"Processing success: {quantifier_simple['processing_success'].mean():.3f}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Example: Look at specific responses\n", "if analyzer and not analyzer.model_df.empty:\n", "    # Show some example responses\n", "    sample_responses = analyzer.model_df.sample(min(5, len(analyzer.model_df)))\n", "    \n", "    print(\"SAMPLE RESPONSES:\")\n", "    print(\"=\" * 20)\n", "    \n", "    for idx, row in sample_responses.iterrows():\n", "        print(f\"\\nScenario: {row.get('target_sentence', 'N/A')}\")\n", "        print(f\"Implicature derived: {row.get('implicature_derived', 'N/A')}\")\n", "        print(f\"Raw response: {row.get('raw_response', 'N/A')[:100]}...\")\n", "        print(\"-\" * 40)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Run New Experiment\n", "\n", "You can also run a quick test experiment from this notebook:"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Uncomment to run a quick test\n", "# exp_config = get_experiment_config({\n", "#     'experiment_name': 'notebook_test',\n", "#     'total_scenarios': 5,\n", "#     'output_directory': '../results/notebook_test'\n", "# })\n", "# \n", "# runner = ExperimentRunner(exp_config)\n", "# test_results = runner.run_quick_test(num_scenarios=3)\n", "# \n", "# print(\"Quick test results:\")\n", "# for key, value in test_results.items():\n", "#     print(f\"{key}: {value}\")"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.0"}}, "nbformat": 4, "nbformat_minor": 4}