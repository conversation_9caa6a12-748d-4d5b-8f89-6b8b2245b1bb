"""
Model configuration for scalar implicature experiments.
"""

import os
from dataclasses import dataclass
from typing import Dict, Any, Optional

@dataclass
class ModelConfig:
    """Configuration for the language model."""
    
    # Model path and loading
    model_path: str = "/Users/<USER>/VibeCoding/Models/Qwen3-4B"
    model_name: str = "Qwen3-4B"
    device: str = "auto"  # "auto", "cpu", "cuda", or specific device
    torch_dtype: str = "auto"  # "auto", "float16", "float32", "bfloat16"
    
    # Generation parameters
    max_length: int = 512
    max_new_tokens: int = 100
    temperature: float = 0.7
    top_p: float = 0.9
    top_k: int = 50
    do_sample: bool = True
    num_return_sequences: int = 1
    pad_token_id: Optional[int] = None
    eos_token_id: Optional[int] = None
    
    # Batch processing
    batch_size: int = 8
    
    # Safety and reliability
    use_cache: bool = True
    trust_remote_code: bool = True
    low_cpu_mem_usage: bool = True
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert config to dictionary."""
        return {
            'model_path': self.model_path,
            'model_name': self.model_name,
            'device': self.device,
            'torch_dtype': self.torch_dtype,
            'max_length': self.max_length,
            'max_new_tokens': self.max_new_tokens,
            'temperature': self.temperature,
            'top_p': self.top_p,
            'top_k': self.top_k,
            'do_sample': self.do_sample,
            'num_return_sequences': self.num_return_sequences,
            'batch_size': self.batch_size,
            'use_cache': self.use_cache,
            'trust_remote_code': self.trust_remote_code,
            'low_cpu_mem_usage': self.low_cpu_mem_usage
        }
    
    def validate(self) -> bool:
        """Validate model configuration."""
        if not os.path.exists(self.model_path):
            raise ValueError(f"Model path does not exist: {self.model_path}")
        
        if self.temperature < 0 or self.temperature > 2:
            raise ValueError(f"Temperature must be between 0 and 2, got {self.temperature}")
        
        if self.top_p < 0 or self.top_p > 1:
            raise ValueError(f"top_p must be between 0 and 1, got {self.top_p}")
        
        if self.max_length <= 0:
            raise ValueError(f"max_length must be positive, got {self.max_length}")
        
        if self.batch_size <= 0:
            raise ValueError(f"batch_size must be positive, got {self.batch_size}")
        
        return True

# Default configuration instance
DEFAULT_MODEL_CONFIG = ModelConfig()

def get_model_config(custom_config: Optional[Dict[str, Any]] = None) -> ModelConfig:
    """
    Get model configuration with optional custom overrides.
    
    Args:
        custom_config: Dictionary of custom configuration values
        
    Returns:
        ModelConfig instance
    """
    config = ModelConfig()
    
    if custom_config:
        for key, value in custom_config.items():
            if hasattr(config, key):
                setattr(config, key, value)
            else:
                raise ValueError(f"Unknown configuration key: {key}")
    
    config.validate()
    return config
