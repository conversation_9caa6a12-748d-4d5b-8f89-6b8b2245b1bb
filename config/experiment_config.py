"""
Experiment configuration for scalar implicature studies.
"""

from dataclasses import dataclass, field
from typing import List, Dict, Any, Optional
import random

@dataclass
class ExperimentConfig:
    """Configuration for scalar implicature experiments."""
    
    # Experiment identification
    experiment_name: str = "scalar_implicature_reproduction"
    version: str = "1.0"
    description: str = "Reproduction of <PERSON>ino<PERSON> et al. (2024) scalar implicature experiment"
    
    # Random seed for reproducibility
    random_seed: int = 42
    
    # Experimental conditions
    test_quantifier_scales: bool = True
    test_adjectival_scales: bool = True
    test_negation_effects: bool = True
    test_context_variations: bool = True
    
    # Scale types to test
    quantifier_scales: List[str] = field(default_factory=lambda: [
        "some_all", "many_most", "few_none", "several_all"
    ])
    
    adjectival_scales: List[str] = field(default_factory=lambda: [
        "warm_hot", "cool_cold", "large_huge", "small_tiny",
        "good_excellent", "bad_terrible", "fast_very_fast", "slow_very_slow"
    ])
    
    # Context types
    context_types: List[str] = field(default_factory=lambda: [
        "simple", "embedded", "question", "conditional"
    ])
    
    # Negation types
    negation_types: List[str] = field(default_factory=lambda: [
        "none", "sentential", "constituent"
    ])
    
    # Sample sizes
    samples_per_condition: int = 50
    total_scenarios: int = 1000
    
    # Evaluation settings
    num_repetitions: int = 3  # Number of times to run each scenario
    confidence_threshold: float = 0.7  # Threshold for confident responses
    
    # Human simulation parameters
    simulate_human_data: bool = True
    human_implicature_rates: Dict[str, float] = field(default_factory=lambda: {
        "quantifier_base": 0.8,  # Base rate for quantifier scales
        "adjectival_base": 0.6,  # Base rate for adjectival scales
        "embedded_penalty": 0.15,  # Reduction in embedded contexts
        "negation_effect": 0.1,  # Effect of negation
        "individual_variation": 0.2  # Standard deviation for individual differences
    })
    
    # Output settings
    save_raw_responses: bool = True
    save_processed_data: bool = True
    save_analysis_plots: bool = True
    output_directory: str = "results"
    
    # Logging
    log_level: str = "INFO"
    log_to_file: bool = True
    
    def __post_init__(self):
        """Post-initialization validation and setup."""
        # Set random seed
        random.seed(self.random_seed)
        
        # Validate configuration
        self.validate()
    
    def validate(self) -> bool:
        """Validate experiment configuration."""
        if self.samples_per_condition <= 0:
            raise ValueError("samples_per_condition must be positive")
        
        if self.total_scenarios <= 0:
            raise ValueError("total_scenarios must be positive")
        
        if self.num_repetitions <= 0:
            raise ValueError("num_repetitions must be positive")
        
        if not (0 <= self.confidence_threshold <= 1):
            raise ValueError("confidence_threshold must be between 0 and 1")
        
        # Validate human simulation parameters
        for key, value in self.human_implicature_rates.items():
            if not (0 <= value <= 1):
                raise ValueError(f"Human rate {key} must be between 0 and 1, got {value}")
        
        return True
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert config to dictionary."""
        return {
            'experiment_name': self.experiment_name,
            'version': self.version,
            'description': self.description,
            'random_seed': self.random_seed,
            'test_quantifier_scales': self.test_quantifier_scales,
            'test_adjectival_scales': self.test_adjectival_scales,
            'test_negation_effects': self.test_negation_effects,
            'test_context_variations': self.test_context_variations,
            'quantifier_scales': self.quantifier_scales,
            'adjectival_scales': self.adjectival_scales,
            'context_types': self.context_types,
            'negation_types': self.negation_types,
            'samples_per_condition': self.samples_per_condition,
            'total_scenarios': self.total_scenarios,
            'num_repetitions': self.num_repetitions,
            'confidence_threshold': self.confidence_threshold,
            'simulate_human_data': self.simulate_human_data,
            'human_implicature_rates': self.human_implicature_rates,
            'save_raw_responses': self.save_raw_responses,
            'save_processed_data': self.save_processed_data,
            'save_analysis_plots': self.save_analysis_plots,
            'output_directory': self.output_directory,
            'log_level': self.log_level,
            'log_to_file': self.log_to_file
        }

# Default configuration instance
DEFAULT_EXPERIMENT_CONFIG = ExperimentConfig()

def get_experiment_config(custom_config: Optional[Dict[str, Any]] = None) -> ExperimentConfig:
    """
    Get experiment configuration with optional custom overrides.
    
    Args:
        custom_config: Dictionary of custom configuration values
        
    Returns:
        ExperimentConfig instance
    """
    config = ExperimentConfig()
    
    if custom_config:
        for key, value in custom_config.items():
            if hasattr(config, key):
                setattr(config, key, value)
            else:
                raise ValueError(f"Unknown configuration key: {key}")
    
    config.validate()
    return config
