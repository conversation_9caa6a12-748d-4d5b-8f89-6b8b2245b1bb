"""
Main experimental pipeline for scalar implicature experiments.
"""

import os
import json
import logging
from datetime import datetime
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import asdict
import pandas as pd
from tqdm import tqdm

from config.experiment_config import ExperimentConfig, get_experiment_config
from config.model_config import ModelConfig, get_model_config
from data.scenarios import ScenarioGenerator, ScalarScenario
from data.human_patterns import HumanPatternSimulator, HumanResponse
from src.model_interface import ModelInterface
from src.prompt_templates import PromptTemplate, PromptType, AdaptivePromptTemplate
from src.response_processor import ResponseProcessor, ResponseType, ProcessedResponse
from src.metrics import ScalarImplicatureMetrics

logger = logging.getLogger(__name__)

class ExperimentRunner:
    """Main class for running scalar implicature experiments."""
    
    def __init__(self, experiment_config: Optional[ExperimentConfig] = None,
                 model_config: Optional[ModelConfig] = None):
        """
        Initialize the experiment runner.
        
        Args:
            experiment_config: Experiment configuration
            model_config: Model configuration
        """
        self.exp_config = experiment_config or get_experiment_config()
        self.model_config = model_config or get_model_config()
        
        # Initialize components
        self.scenario_generator = ScenarioGenerator()
        self.human_simulator = HumanPatternSimulator(
            base_rates=self.exp_config.human_implicature_rates,
            individual_variation=self.exp_config.human_implicature_rates.get('individual_variation', 0.2)
        )
        self.prompt_template = AdaptivePromptTemplate()
        self.response_processor = ResponseProcessor()
        self.metrics_calculator = ScalarImplicatureMetrics()
        
        # Model interface (loaded on demand)
        self.model_interface = None
        
        # Results storage
        self.results = {
            'scenarios': [],
            'model_responses': [],
            'human_responses': [],
            'processed_responses': [],
            'metrics': {},
            'metadata': {}
        }
        
        # Setup logging
        self._setup_logging()
        
        # Create output directory
        os.makedirs(self.exp_config.output_directory, exist_ok=True)
    
    def _setup_logging(self):
        """Setup logging configuration."""
        log_level = getattr(logging, self.exp_config.log_level.upper())
        
        # Configure logger
        logging.basicConfig(
            level=log_level,
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        )
        
        # Add file handler if requested
        if self.exp_config.log_to_file:
            log_file = os.path.join(self.exp_config.output_directory, 'experiment.log')
            file_handler = logging.FileHandler(log_file)
            file_handler.setLevel(log_level)
            formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
            file_handler.setFormatter(formatter)
            logger.addHandler(file_handler)
    
    def _load_model(self) -> bool:
        """Load the language model."""
        if self.model_interface is None:
            logger.info("Loading language model...")
            self.model_interface = ModelInterface(self.model_config)
            
            if not self.model_interface.load_model():
                logger.error("Failed to load model")
                return False
            
            # Test the model
            if not self.model_interface.test_model():
                logger.error("Model test failed")
                return False
            
            logger.info("Model loaded and tested successfully")
        
        return True
    
    def generate_scenarios(self) -> List[ScalarScenario]:
        """Generate experimental scenarios."""
        logger.info(f"Generating {self.exp_config.total_scenarios} scenarios...")
        
        scenarios = self.scenario_generator.generate_scenario_set(
            num_scenarios=self.exp_config.total_scenarios
        )
        
        logger.info(f"Generated {len(scenarios)} scenarios")
        return scenarios
    
    def run_model_experiment(self, scenarios: List[ScalarScenario], 
                           prompt_type: PromptType = PromptType.INFERENCE) -> List[ProcessedResponse]:
        """
        Run the model experiment on given scenarios.
        
        Args:
            scenarios: List of scenarios to test
            prompt_type: Type of prompt to use
            
        Returns:
            List of processed responses
        """
        if not self._load_model():
            raise RuntimeError("Failed to load model")
        
        logger.info(f"Running model experiment on {len(scenarios)} scenarios...")
        
        all_responses = []
        
        # Process scenarios in batches
        batch_size = min(self.model_config.batch_size, 10)  # Limit batch size for memory
        
        for i in tqdm(range(0, len(scenarios), batch_size), desc="Processing scenarios"):
            batch_scenarios = scenarios[i:i + batch_size]
            
            # Generate prompts for batch
            prompts = []
            for scenario in batch_scenarios:
                prompt = self.prompt_template.generate_adaptive_prompt(scenario, prompt_type)
                prompts.append(prompt)
            
            # Get model responses
            try:
                raw_responses = []
                for prompt in prompts:
                    response = self.model_interface.generate_response(prompt)
                    raw_responses.append(response)
                
                # Process responses
                response_type = ResponseType.INFERENCE if prompt_type == PromptType.INFERENCE else ResponseType.BINARY
                
                for raw_response, scenario in zip(raw_responses, batch_scenarios):
                    processed = self.response_processor.process_response(
                        raw_response, scenario, response_type
                    )
                    all_responses.append(processed)
                
            except Exception as e:
                logger.error(f"Error processing batch {i//batch_size + 1}: {str(e)}")
                # Add empty responses for failed batch
                for scenario in batch_scenarios:
                    failed_response = ProcessedResponse(
                        raw_response="",
                        scenario_id=scenario.scenario_id,
                        response_type=response_type,
                        processing_success=False,
                        processing_notes=f"Batch processing failed: {str(e)}"
                    )
                    all_responses.append(failed_response)
        
        logger.info(f"Completed model experiment. Processed {len(all_responses)} responses")
        return all_responses
    
    def simulate_human_experiment(self, scenarios: List[ScalarScenario], 
                                 num_participants: int = None) -> List[HumanResponse]:
        """
        Simulate human responses to scenarios.
        
        Args:
            scenarios: List of scenarios to test
            num_participants: Number of participants to simulate
            
        Returns:
            List of simulated human responses
        """
        if not self.exp_config.simulate_human_data:
            return []
        
        num_participants = num_participants or 50
        logger.info(f"Simulating human experiment with {num_participants} participants...")
        
        human_responses = self.human_simulator.simulate_experiment(scenarios, num_participants)
        
        logger.info(f"Generated {len(human_responses)} human responses")
        return human_responses
    
    def calculate_metrics(self, model_responses: List[ProcessedResponse], 
                         scenarios: List[ScalarScenario],
                         human_responses: Optional[List[HumanResponse]] = None) -> Dict[str, Any]:
        """
        Calculate evaluation metrics.
        
        Args:
            model_responses: Model responses
            scenarios: Scenarios
            human_responses: Optional human responses
            
        Returns:
            Dictionary of metrics
        """
        logger.info("Calculating evaluation metrics...")
        
        metrics = self.metrics_calculator.calculate_all_metrics(
            model_responses, scenarios, human_responses
        )
        
        # Export metrics summary
        metrics_summary = self.metrics_calculator.export_metrics_summary(metrics)
        
        logger.info(f"Calculated {len(metrics)} metrics")
        return metrics_summary
    
    def save_results(self, scenarios: List[ScalarScenario], 
                    model_responses: List[ProcessedResponse],
                    human_responses: List[HumanResponse],
                    metrics: Dict[str, Any]) -> str:
        """
        Save experimental results.
        
        Args:
            scenarios: Experimental scenarios
            model_responses: Model responses
            human_responses: Human responses
            metrics: Calculated metrics
            
        Returns:
            Path to saved results file
        """
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        results_file = os.path.join(
            self.exp_config.output_directory, 
            f"experiment_results_{timestamp}.json"
        )
        
        # Prepare results data
        results_data = {
            'metadata': {
                'timestamp': timestamp,
                'experiment_config': self.exp_config.to_dict(),
                'model_config': self.model_config.to_dict(),
                'num_scenarios': len(scenarios),
                'num_model_responses': len(model_responses),
                'num_human_responses': len(human_responses)
            },
            'scenarios': [
                {
                    'scenario_id': s.scenario_id,
                    'scale_weak': s.scale.weak_term,
                    'scale_strong': s.scale.strong_term,
                    'scale_type': s.scale.scale_type.value,
                    'context_type': s.context_type.value,
                    'negation_type': s.negation_type.value,
                    'target_sentence': s.target_sentence,
                    'expected_implicature': s.expected_implicature
                }
                for s in scenarios
            ],
            'model_responses': [
                {
                    'scenario_id': r.scenario_id,
                    'raw_response': r.raw_response,
                    'implicature_derived': r.implicature_derived,
                    'binary_answer': r.binary_answer,
                    'confidence_score': r.confidence_score,
                    'processing_success': r.processing_success,
                    'processing_notes': r.processing_notes
                }
                for r in model_responses
            ],
            'human_responses': [
                {
                    'scenario_id': r.scenario_id,
                    'participant_id': r.participant_id,
                    'implicature_derived': r.implicature_derived,
                    'confidence': r.confidence,
                    'response_time': r.response_time
                }
                for r in human_responses
            ],
            'metrics': metrics
        }
        
        # Save to file
        with open(results_file, 'w') as f:
            json.dump(results_data, f, indent=2)
        
        logger.info(f"Results saved to {results_file}")
        return results_file
    
    def run_complete_experiment(self, prompt_type: PromptType = PromptType.INFERENCE) -> str:
        """
        Run the complete experimental pipeline.
        
        Args:
            prompt_type: Type of prompt to use for model testing
            
        Returns:
            Path to results file
        """
        logger.info("Starting complete scalar implicature experiment...")
        
        try:
            # Generate scenarios
            scenarios = self.generate_scenarios()
            
            # Run model experiment
            model_responses = self.run_model_experiment(scenarios, prompt_type)
            
            # Simulate human responses
            human_responses = self.simulate_human_experiment(scenarios)
            
            # Calculate metrics
            metrics = self.calculate_metrics(model_responses, scenarios, human_responses)
            
            # Save results
            results_file = self.save_results(scenarios, model_responses, human_responses, metrics)
            
            logger.info("Experiment completed successfully!")
            return results_file
            
        except Exception as e:
            logger.error(f"Experiment failed: {str(e)}")
            raise
        
        finally:
            # Cleanup model resources
            if self.model_interface:
                self.model_interface.cleanup()
    
    def run_quick_test(self, num_scenarios: int = 10) -> Dict[str, Any]:
        """
        Run a quick test with a small number of scenarios.
        
        Args:
            num_scenarios: Number of scenarios to test
            
        Returns:
            Quick test results
        """
        logger.info(f"Running quick test with {num_scenarios} scenarios...")
        
        # Generate small set of scenarios
        scenarios = self.scenario_generator.generate_scenario_set(num_scenarios)
        
        # Load model
        if not self._load_model():
            raise RuntimeError("Failed to load model for quick test")
        
        # Test a few scenarios
        test_results = []
        for i, scenario in enumerate(scenarios[:3]):  # Test first 3
            prompt = self.prompt_template.generate_adaptive_prompt(scenario, PromptType.INFERENCE)
            response = self.model_interface.generate_response(prompt, max_new_tokens=50)
            processed = self.response_processor.process_response(
                response, scenario, ResponseType.INFERENCE
            )
            
            test_results.append({
                'scenario_id': scenario.scenario_id,
                'target_sentence': scenario.target_sentence,
                'raw_response': response,
                'implicature_derived': processed.implicature_derived,
                'processing_success': processed.processing_success
            })
        
        logger.info("Quick test completed")
        return {
            'num_scenarios_generated': len(scenarios),
            'test_results': test_results,
            'model_info': self.model_interface.get_model_info()
        }
