"""
Interface for loading and using the local language model for scalar implicature experiments.
"""

import torch
import logging
from typing import List, Dict, Any, Optional, Union
from transformers import (
    AutoTokenizer, 
    AutoModelForCausalLM, 
    GenerationConfig,
    pipeline
)
from config.model_config import ModelConfig, get_model_config

logger = logging.getLogger(__name__)

class ModelInterface:
    """Interface for the local language model."""
    
    def __init__(self, config: Optional[ModelConfig] = None):
        """
        Initialize the model interface.
        
        Args:
            config: Model configuration. If None, uses default config.
        """
        self.config = config or get_model_config()
        self.tokenizer = None
        self.model = None
        self.generation_config = None
        self.device = None
        self._setup_device()
        
    def _setup_device(self):
        """Setup the appropriate device for model inference."""
        if self.config.device == "auto":
            if torch.cuda.is_available():
                self.device = "cuda"
            elif torch.backends.mps.is_available():
                self.device = "mps"
            else:
                self.device = "cpu"
        else:
            self.device = self.config.device
        
        logger.info(f"Using device: {self.device}")
    
    def load_model(self) -> bool:
        """
        Load the tokenizer and model.
        
        Returns:
            True if successful, False otherwise
        """
        try:
            logger.info(f"Loading model from {self.config.model_path}")
            
            # Load tokenizer
            self.tokenizer = AutoTokenizer.from_pretrained(
                self.config.model_path,
                trust_remote_code=self.config.trust_remote_code
            )
            
            # Set pad token if not present
            if self.tokenizer.pad_token is None:
                self.tokenizer.pad_token = self.tokenizer.eos_token
            
            # Load model
            torch_dtype = getattr(torch, self.config.torch_dtype) if self.config.torch_dtype != "auto" else "auto"
            
            self.model = AutoModelForCausalLM.from_pretrained(
                self.config.model_path,
                torch_dtype=torch_dtype,
                device_map=self.device if self.device != "cpu" else None,
                trust_remote_code=self.config.trust_remote_code,
                low_cpu_mem_usage=self.config.low_cpu_mem_usage
            )
            
            # Move to device if CPU
            if self.device == "cpu":
                self.model = self.model.to(self.device)
            
            # Setup generation config
            self.generation_config = GenerationConfig(
                max_length=self.config.max_length,
                max_new_tokens=self.config.max_new_tokens,
                temperature=self.config.temperature,
                top_p=self.config.top_p,
                top_k=self.config.top_k,
                do_sample=self.config.do_sample,
                num_return_sequences=self.config.num_return_sequences,
                pad_token_id=self.tokenizer.pad_token_id,
                eos_token_id=self.tokenizer.eos_token_id,
                use_cache=self.config.use_cache
            )
            
            logger.info("Model loaded successfully")
            return True
            
        except Exception as e:
            logger.error(f"Failed to load model: {str(e)}")
            return False
    
    def generate_response(self, prompt: str, **kwargs) -> str:
        """
        Generate a response to a prompt.
        
        Args:
            prompt: Input prompt
            **kwargs: Additional generation parameters
            
        Returns:
            Generated response text
        """
        if self.model is None or self.tokenizer is None:
            raise RuntimeError("Model not loaded. Call load_model() first.")
        
        try:
            # Tokenize input
            inputs = self.tokenizer(prompt, return_tensors="pt", padding=True, truncation=True)
            inputs = {k: v.to(self.device) for k, v in inputs.items()}
            
            # Update generation config with any provided kwargs
            gen_config = self.generation_config
            if kwargs:
                gen_config = GenerationConfig(**{**self.generation_config.to_dict(), **kwargs})
            
            # Generate response
            with torch.no_grad():
                outputs = self.model.generate(
                    **inputs,
                    generation_config=gen_config
                )
            
            # Decode response
            response = self.tokenizer.decode(
                outputs[0][inputs['input_ids'].shape[1]:], 
                skip_special_tokens=True
            )
            
            return response.strip()
            
        except Exception as e:
            logger.error(f"Generation failed: {str(e)}")
            return ""
    
    def generate_batch_responses(self, prompts: List[str], **kwargs) -> List[str]:
        """
        Generate responses to multiple prompts in batch.
        
        Args:
            prompts: List of input prompts
            **kwargs: Additional generation parameters
            
        Returns:
            List of generated response texts
        """
        if self.model is None or self.tokenizer is None:
            raise RuntimeError("Model not loaded. Call load_model() first.")
        
        responses = []
        batch_size = self.config.batch_size
        
        for i in range(0, len(prompts), batch_size):
            batch_prompts = prompts[i:i + batch_size]
            batch_responses = []
            
            for prompt in batch_prompts:
                response = self.generate_response(prompt, **kwargs)
                batch_responses.append(response)
            
            responses.extend(batch_responses)
        
        return responses
    
    def test_model(self) -> bool:
        """
        Test if the model is working correctly.
        
        Returns:
            True if test passes, False otherwise
        """
        if self.model is None or self.tokenizer is None:
            logger.error("Model not loaded for testing")
            return False
        
        try:
            test_prompt = "The weather is warm today."
            response = self.generate_response(test_prompt, max_new_tokens=20)
            
            if response:
                logger.info(f"Model test successful. Response: {response[:50]}...")
                return True
            else:
                logger.error("Model test failed: empty response")
                return False
                
        except Exception as e:
            logger.error(f"Model test failed: {str(e)}")
            return False
    
    def get_model_info(self) -> Dict[str, Any]:
        """
        Get information about the loaded model.
        
        Returns:
            Dictionary with model information
        """
        if self.model is None:
            return {"status": "not_loaded"}
        
        try:
            return {
                "status": "loaded",
                "model_path": self.config.model_path,
                "model_name": self.config.model_name,
                "device": self.device,
                "torch_dtype": str(self.model.dtype) if hasattr(self.model, 'dtype') else "unknown",
                "vocab_size": self.tokenizer.vocab_size if self.tokenizer else "unknown",
                "max_length": self.config.max_length,
                "generation_config": self.generation_config.to_dict() if self.generation_config else {}
            }
        except Exception as e:
            logger.error(f"Failed to get model info: {str(e)}")
            return {"status": "error", "error": str(e)}
    
    def cleanup(self):
        """Clean up model resources."""
        if self.model is not None:
            del self.model
            self.model = None
        
        if self.tokenizer is not None:
            del self.tokenizer
            self.tokenizer = None
        
        if torch.cuda.is_available():
            torch.cuda.empty_cache()
        
        logger.info("Model resources cleaned up")

# Convenience function for quick model setup
def create_model_interface(model_path: str = None, **config_kwargs) -> ModelInterface:
    """
    Create and load a model interface with custom configuration.
    
    Args:
        model_path: Path to the model (overrides config)
        **config_kwargs: Additional configuration parameters
        
    Returns:
        Loaded ModelInterface instance
    """
    config_dict = config_kwargs.copy()
    if model_path:
        config_dict['model_path'] = model_path
    
    config = get_model_config(config_dict)
    interface = ModelInterface(config)
    
    if interface.load_model():
        return interface
    else:
        raise RuntimeError("Failed to load model interface")
