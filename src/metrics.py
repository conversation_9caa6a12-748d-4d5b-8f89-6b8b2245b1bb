"""
Evaluation metrics for scalar implicature experiments.
"""

import numpy as np
import pandas as pd
from typing import Dict, List, Any, Tu<PERSON>, Optional
from dataclasses import dataclass
from scipy import stats
from collections import defaultdict

from data.scenarios import ScalarScenario, ScaleType, ContextType, NegationType
from data.human_patterns import HumanResponse
from src.response_processor import ProcessedResponse

@dataclass
class MetricResult:
    """Result of a metric calculation."""
    name: str
    value: float
    confidence_interval: Optional[Tuple[float, float]] = None
    p_value: Optional[float] = None
    effect_size: Optional[float] = None
    description: str = ""

class ScalarImplicatureMetrics:
    """Metrics for evaluating scalar implicature behavior."""
    
    def __init__(self):
        self.metrics = {}
    
    def calculate_implicature_rate(self, responses: List[ProcessedResponse], 
                                  condition_filter: Optional[Dict[str, Any]] = None) -> MetricResult:
        """
        Calculate the rate of scalar implicature derivation.
        
        Args:
            responses: List of processed responses
            condition_filter: Optional filter for specific conditions
            
        Returns:
            Met<PERSON>R<PERSON>ult with implicature rate
        """
        # Filter responses if condition specified
        if condition_filter:
            # This would require access to scenarios - simplified for now
            filtered_responses = responses
        else:
            filtered_responses = responses
        
        # Count implicature derivations
        valid_responses = [r for r in filtered_responses if r.implicature_derived is not None]
        if not valid_responses:
            return MetricResult("implicature_rate", 0.0, description="No valid responses")
        
        implicature_count = sum(1 for r in valid_responses if r.implicature_derived)
        rate = implicature_count / len(valid_responses)
        
        # Calculate confidence interval (Wilson score interval)
        n = len(valid_responses)
        if n > 0:
            z = 1.96  # 95% confidence
            p = rate
            ci_lower = (p + z*z/(2*n) - z*np.sqrt((p*(1-p) + z*z/(4*n))/n)) / (1 + z*z/n)
            ci_upper = (p + z*z/(2*n) + z*np.sqrt((p*(1-p) + z*z/(4*n))/n)) / (1 + z*z/n)
            ci = (max(0, ci_lower), min(1, ci_upper))
        else:
            ci = None
        
        return MetricResult(
            name="implicature_rate",
            value=rate,
            confidence_interval=ci,
            description=f"Proportion of responses deriving scalar implicature ({implicature_count}/{n})"
        )
    
    def calculate_consistency_score(self, responses: List[ProcessedResponse]) -> MetricResult:
        """
        Calculate response consistency across similar scenarios.
        
        Args:
            responses: List of processed responses
            
        Returns:
            MetricResult with consistency score
        """
        # Group responses by scenario characteristics
        # This is simplified - would need access to full scenario data
        scenario_groups = defaultdict(list)
        
        for response in responses:
            if response.implicature_derived is not None:
                # Use scenario_id prefix as a simple grouping mechanism
                group_key = response.scenario_id.split('_')[0] if '_' in response.scenario_id else response.scenario_id
                scenario_groups[group_key].append(response.implicature_derived)
        
        # Calculate consistency within each group
        group_consistencies = []
        for group, decisions in scenario_groups.items():
            if len(decisions) > 1:
                # Calculate proportion of majority decision
                true_count = sum(decisions)
                false_count = len(decisions) - true_count
                majority_count = max(true_count, false_count)
                consistency = majority_count / len(decisions)
                group_consistencies.append(consistency)
        
        if not group_consistencies:
            return MetricResult("consistency_score", 0.0, description="No groups with multiple responses")
        
        overall_consistency = np.mean(group_consistencies)
        
        return MetricResult(
            name="consistency_score",
            value=overall_consistency,
            description=f"Average within-group consistency across {len(group_consistencies)} groups"
        )
    
    def calculate_context_sensitivity(self, responses: List[ProcessedResponse], 
                                    scenarios: List[ScalarScenario]) -> MetricResult:
        """
        Calculate how much responses vary with context changes.
        
        Args:
            responses: List of processed responses
            scenarios: List of corresponding scenarios
            
        Returns:
            MetricResult with context sensitivity score
        """
        # Create response-scenario pairs
        response_scenario_pairs = list(zip(responses, scenarios))
        
        # Group by scale and negation, compare across contexts
        scale_groups = defaultdict(lambda: defaultdict(list))
        
        for response, scenario in response_scenario_pairs:
            if response.implicature_derived is not None:
                scale_key = f"{scenario.scale.weak_term}_{scenario.scale.strong_term}"
                context_key = f"{scenario.context_type.value}_{scenario.negation_type.value}"
                scale_groups[scale_key][context_key].append(response.implicature_derived)
        
        # Calculate variance across contexts for each scale
        context_variances = []
        for scale_key, context_data in scale_groups.items():
            if len(context_data) > 1:  # Need multiple contexts
                context_rates = []
                for context_key, decisions in context_data.items():
                    if decisions:  # Non-empty
                        rate = sum(decisions) / len(decisions)
                        context_rates.append(rate)
                
                if len(context_rates) > 1:
                    variance = np.var(context_rates)
                    context_variances.append(variance)
        
        if not context_variances:
            return MetricResult("context_sensitivity", 0.0, description="Insufficient data for context comparison")
        
        sensitivity = np.mean(context_variances)
        
        return MetricResult(
            name="context_sensitivity",
            value=sensitivity,
            description=f"Average variance in implicature rates across contexts ({len(context_variances)} scales)"
        )
    
    def calculate_scale_type_effect(self, responses: List[ProcessedResponse], 
                                   scenarios: List[ScalarScenario]) -> MetricResult:
        """
        Calculate difference in implicature rates between scale types.
        
        Args:
            responses: List of processed responses
            scenarios: List of corresponding scenarios
            
        Returns:
            MetricResult with scale type effect size
        """
        # Separate responses by scale type
        quantifier_responses = []
        adjectival_responses = []
        
        for response, scenario in zip(responses, scenarios):
            if response.implicature_derived is not None:
                if scenario.scale.scale_type == ScaleType.QUANTIFIER:
                    quantifier_responses.append(response.implicature_derived)
                else:
                    adjectival_responses.append(response.implicature_derived)
        
        if not quantifier_responses or not adjectival_responses:
            return MetricResult("scale_type_effect", 0.0, description="Insufficient data for both scale types")
        
        # Calculate rates
        quantifier_rate = sum(quantifier_responses) / len(quantifier_responses)
        adjectival_rate = sum(adjectival_responses) / len(adjectival_responses)
        
        # Effect size (difference in rates)
        effect_size = quantifier_rate - adjectival_rate
        
        # Statistical test
        contingency_table = [
            [sum(quantifier_responses), len(quantifier_responses) - sum(quantifier_responses)],
            [sum(adjectival_responses), len(adjectival_responses) - sum(adjectival_responses)]
        ]
        
        try:
            chi2, p_value, _, _ = stats.chi2_contingency(contingency_table)
        except:
            p_value = None
        
        return MetricResult(
            name="scale_type_effect",
            value=effect_size,
            p_value=p_value,
            effect_size=effect_size,
            description=f"Difference in implicature rates: Quantifier ({quantifier_rate:.3f}) - Adjectival ({adjectival_rate:.3f})"
        )
    
    def compare_to_human_baseline(self, model_responses: List[ProcessedResponse], 
                                 human_responses: List[HumanResponse]) -> MetricResult:
        """
        Compare model behavior to human baseline.
        
        Args:
            model_responses: Model responses
            human_responses: Human responses
            
        Returns:
            MetricResult with comparison statistics
        """
        # Calculate model implicature rate
        model_valid = [r for r in model_responses if r.implicature_derived is not None]
        if not model_valid:
            return MetricResult("human_comparison", 0.0, description="No valid model responses")
        
        model_rate = sum(1 for r in model_valid if r.implicature_derived) / len(model_valid)
        
        # Calculate human implicature rate
        if not human_responses:
            return MetricResult("human_comparison", 0.0, description="No human responses")
        
        human_rate = sum(1 for r in human_responses if r.implicature_derived) / len(human_responses)
        
        # Calculate difference
        difference = abs(model_rate - human_rate)
        
        # Statistical test (if sample sizes are adequate)
        if len(model_valid) > 10 and len(human_responses) > 10:
            model_successes = sum(1 for r in model_valid if r.implicature_derived)
            human_successes = sum(1 for r in human_responses if r.implicature_derived)
            
            # Two-proportion z-test
            p1 = model_successes / len(model_valid)
            p2 = human_successes / len(human_responses)
            n1, n2 = len(model_valid), len(human_responses)
            
            p_pooled = (model_successes + human_successes) / (n1 + n2)
            se = np.sqrt(p_pooled * (1 - p_pooled) * (1/n1 + 1/n2))
            
            if se > 0:
                z_score = (p1 - p2) / se
                p_value = 2 * (1 - stats.norm.cdf(abs(z_score)))
            else:
                p_value = None
        else:
            p_value = None
        
        return MetricResult(
            name="human_comparison",
            value=difference,
            p_value=p_value,
            description=f"Absolute difference from human baseline: Model ({model_rate:.3f}) vs Human ({human_rate:.3f})"
        )
    
    def calculate_all_metrics(self, model_responses: List[ProcessedResponse], 
                             scenarios: List[ScalarScenario],
                             human_responses: Optional[List[HumanResponse]] = None) -> Dict[str, MetricResult]:
        """
        Calculate all available metrics.
        
        Args:
            model_responses: Model responses
            scenarios: Corresponding scenarios
            human_responses: Optional human responses for comparison
            
        Returns:
            Dictionary of metric results
        """
        results = {}
        
        # Basic metrics
        results["implicature_rate"] = self.calculate_implicature_rate(model_responses)
        results["consistency_score"] = self.calculate_consistency_score(model_responses)
        
        # Context and scale effects
        if scenarios:
            results["context_sensitivity"] = self.calculate_context_sensitivity(model_responses, scenarios)
            results["scale_type_effect"] = self.calculate_scale_type_effect(model_responses, scenarios)
        
        # Human comparison
        if human_responses:
            results["human_comparison"] = self.compare_to_human_baseline(model_responses, human_responses)
        
        return results
    
    def export_metrics_summary(self, metrics: Dict[str, MetricResult]) -> Dict[str, Any]:
        """Export metrics in a summary format."""
        summary = {
            "metrics": {},
            "overall_assessment": ""
        }
        
        for name, result in metrics.items():
            summary["metrics"][name] = {
                "value": result.value,
                "confidence_interval": result.confidence_interval,
                "p_value": result.p_value,
                "effect_size": result.effect_size,
                "description": result.description
            }
        
        # Generate overall assessment
        if "implicature_rate" in metrics:
            rate = metrics["implicature_rate"].value
            if rate > 0.7:
                assessment = "High implicature derivation rate"
            elif rate > 0.4:
                assessment = "Moderate implicature derivation rate"
            else:
                assessment = "Low implicature derivation rate"
            
            summary["overall_assessment"] = assessment
        
        return summary
