"""
Prompt templates for scalar implicature experiments.
"""

from typing import Dict, List, Any, Optional
from enum import Enum
from data.scenarios import ScalarScenario, ScaleType, ContextType, NegationType

class PromptType(Enum):
    """Types of prompts for different experimental tasks."""
    TRUTH_VALUE = "truth_value"
    COMPLETION = "completion"
    INFERENCE = "inference"
    PARAPHRASE = "paraphrase"
    EXPLANATION = "explanation"

class PromptTemplate:
    """Template for generating prompts for scalar implicature tasks."""
    
    def __init__(self):
        self.templates = self._initialize_templates()
    
    def _initialize_templates(self) -> Dict[str, Dict[str, str]]:
        """Initialize prompt templates for different task types."""
        return {
            "truth_value": {
                "instruction": "Read the following scenario and statement. Then answer whether the statement is true or false, and explain your reasoning.",
                "template": """Scenario: {scenario_text}

Statement: "{target_sentence}"

Question: Is this statement true or false? Please explain your reasoning.

Answer:""",
                "follow_up": "Based on your answer, what can you infer about the situation?"
            },
            
            "completion": {
                "instruction": "Complete the following sentence in a way that makes sense given the context.",
                "template": """Context: {scenario_text}

Complete this sentence: "{target_sentence}, so..."

Completion:""",
                "follow_up": "What does your completion tell us about the original statement?"
            },
            
            "inference": {
                "instruction": "Read the statement and tell me what you can reasonably infer from it.",
                "template": """Statement: "{target_sentence}"

Question: What can you reasonably infer from this statement? List all the inferences you can make.

Inferences:""",
                "follow_up": "Are there any stronger alternatives that the speaker could have used instead?"
            },
            
            "paraphrase": {
                "instruction": "Rephrase the following statement to make its meaning as clear and explicit as possible.",
                "template": """Original statement: "{target_sentence}"

Question: Please rephrase this statement to make its meaning as clear and explicit as possible.

Rephrased statement:""",
                "follow_up": "How does your rephrasing differ from the original statement?"
            },
            
            "explanation": {
                "instruction": "Explain what the speaker means when they say the following statement.",
                "template": """Statement: "{target_sentence}"

Question: What does the speaker really mean when they say this? Explain the full meaning, including any implied information.

Explanation:""",
                "follow_up": "Why might the speaker choose to say it this way instead of being more direct?"
            }
        }
    
    def generate_prompt(self, scenario: ScalarScenario, prompt_type: PromptType, 
                       include_instruction: bool = True, include_follow_up: bool = False) -> str:
        """
        Generate a prompt for a scalar implicature scenario.
        
        Args:
            scenario: The scalar implicature scenario
            prompt_type: Type of prompt to generate
            include_instruction: Whether to include task instructions
            include_follow_up: Whether to include follow-up questions
            
        Returns:
            Generated prompt string
        """
        template_data = self.templates[prompt_type.value]
        
        # Build the prompt
        prompt_parts = []
        
        # Add instruction if requested
        if include_instruction:
            prompt_parts.append(template_data["instruction"])
            prompt_parts.append("")  # Empty line
        
        # Add main template
        main_prompt = template_data["template"].format(
            scenario_text=scenario.scenario_text,
            target_sentence=scenario.target_sentence
        )
        prompt_parts.append(main_prompt)
        
        # Add follow-up if requested
        if include_follow_up and "follow_up" in template_data:
            prompt_parts.append("")  # Empty line
            prompt_parts.append(template_data["follow_up"])
        
        return "\n".join(prompt_parts)
    
    def generate_batch_prompts(self, scenarios: List[ScalarScenario], 
                              prompt_type: PromptType, **kwargs) -> List[str]:
        """
        Generate prompts for multiple scenarios.
        
        Args:
            scenarios: List of scalar implicature scenarios
            prompt_type: Type of prompt to generate
            **kwargs: Additional arguments for generate_prompt
            
        Returns:
            List of generated prompt strings
        """
        return [self.generate_prompt(scenario, prompt_type, **kwargs) for scenario in scenarios]

class AdaptivePromptTemplate(PromptTemplate):
    """Adaptive prompt template that adjusts based on scenario characteristics."""
    
    def __init__(self):
        super().__init__()
        self.adaptive_templates = self._initialize_adaptive_templates()
    
    def _initialize_adaptive_templates(self) -> Dict[str, Dict[str, str]]:
        """Initialize adaptive templates for different scenario types."""
        return {
            "quantifier_simple": {
                "truth_value": """Consider this statement: "{target_sentence}"

In a typical situation, would this statement be true if ALL of the items/people mentioned had the property? 

Please answer YES or NO and explain your reasoning.

Answer:""",
                "inference": """Statement: "{target_sentence}"

What does this tell us about the total number or proportion? What can we rule out?

Analysis:"""
            },
            
            "adjectival_simple": {
                "truth_value": """Consider this statement: "{target_sentence}"

Would this statement still be appropriate if the item was at the extreme end of the scale (e.g., very hot instead of warm)?

Please answer YES or NO and explain your reasoning.

Answer:""",
                "inference": """Statement: "{target_sentence}"

What does this tell us about the intensity or degree? What stronger descriptions can we rule out?

Analysis:"""
            },
            
            "negation_context": {
                "truth_value": """Consider this statement: "{target_sentence}"

This statement involves negation. What does it tell us about what IS the case, not just what ISN'T?

Analysis:""",
                "inference": """Statement: "{target_sentence}"

Given the negation in this statement, what positive inferences can we make?

Inferences:"""
            }
        }
    
    def _get_scenario_category(self, scenario: ScalarScenario) -> str:
        """Determine the category of a scenario for adaptive prompting."""
        if scenario.negation_type != NegationType.NONE:
            return "negation_context"
        elif scenario.scale.scale_type == ScaleType.QUANTIFIER:
            return "quantifier_simple"
        else:
            return "adjectival_simple"
    
    def generate_adaptive_prompt(self, scenario: ScalarScenario, prompt_type: PromptType) -> str:
        """
        Generate an adaptive prompt based on scenario characteristics.
        
        Args:
            scenario: The scalar implicature scenario
            prompt_type: Type of prompt to generate
            
        Returns:
            Generated adaptive prompt string
        """
        category = self._get_scenario_category(scenario)
        
        # Check if we have an adaptive template for this category and prompt type
        if (category in self.adaptive_templates and 
            prompt_type.value in self.adaptive_templates[category]):
            
            template = self.adaptive_templates[category][prompt_type.value]
            return template.format(target_sentence=scenario.target_sentence)
        else:
            # Fall back to standard template
            return self.generate_prompt(scenario, prompt_type)

class PromptVariationGenerator:
    """Generates variations of prompts to test robustness."""
    
    def __init__(self):
        self.variations = self._initialize_variations()
    
    def _initialize_variations(self) -> Dict[str, List[str]]:
        """Initialize prompt variations."""
        return {
            "question_starters": [
                "What can you infer from:",
                "What does this statement tell us:",
                "What is implied by:",
                "What can we conclude from:",
                "What does this suggest:"
            ],
            
            "instruction_styles": [
                "Please analyze the following statement carefully.",
                "Consider the following statement and its implications.",
                "Read this statement and think about what it means.",
                "Examine this statement and its possible interpretations."
            ],
            
            "response_formats": [
                "Please provide a clear yes/no answer followed by your reasoning.",
                "Answer with your conclusion and explain your thinking.",
                "Give your response and justify it with evidence.",
                "State your answer and provide supporting arguments."
            ]
        }
    
    def generate_variations(self, base_prompt: str, num_variations: int = 3) -> List[str]:
        """
        Generate variations of a base prompt.
        
        Args:
            base_prompt: The original prompt
            num_variations: Number of variations to generate
            
        Returns:
            List of prompt variations
        """
        variations = [base_prompt]  # Include original
        
        # Generate variations by modifying different components
        import random
        
        for _ in range(num_variations - 1):
            modified_prompt = base_prompt
            
            # Randomly modify instruction style
            if random.random() < 0.5:
                old_instruction = "Read the following scenario and statement."
                if old_instruction in modified_prompt:
                    new_instruction = random.choice(self.variations["instruction_styles"])
                    modified_prompt = modified_prompt.replace(old_instruction, new_instruction)
            
            # Randomly modify question starter
            if random.random() < 0.5:
                for old_starter in ["What can you infer", "What does this tell us"]:
                    if old_starter in modified_prompt:
                        new_starter = random.choice(self.variations["question_starters"])
                        modified_prompt = modified_prompt.replace(old_starter, new_starter)
                        break
            
            variations.append(modified_prompt)
        
        return variations
