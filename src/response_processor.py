"""
Response processing for scalar implicature experiments.
"""

import re
import logging
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass
from enum import Enum
from data.scenarios import ScalarScenario, ScaleType

logger = logging.getLogger(__name__)

class ResponseType(Enum):
    """Types of responses that can be extracted."""
    BINARY = "binary"  # Yes/No, True/False
    IMPLICATURE = "implicature"  # Whether scalar implicature was derived
    EXPLANATION = "explanation"  # Textual explanation
    CONFIDENCE = "confidence"  # Confidence level
    COMPLETION = "completion"  # Sentence completion

@dataclass
class ProcessedResponse:
    """Processed response from the language model."""
    raw_response: str
    scenario_id: str
    response_type: ResponseType
    binary_answer: Optional[bool] = None
    implicature_derived: Optional[bool] = None
    confidence_score: Optional[float] = None
    explanation: Optional[str] = None
    completion: Optional[str] = None
    processing_success: bool = True
    processing_notes: str = ""

class ResponseProcessor:
    """Processes language model responses for scalar implicature analysis."""
    
    def __init__(self):
        self.patterns = self._initialize_patterns()
        self.implicature_indicators = self._initialize_implicature_indicators()
    
    def _initialize_patterns(self) -> Dict[str, List[str]]:
        """Initialize regex patterns for response parsing."""
        return {
            "yes_patterns": [
                r'\byes\b', r'\btrue\b', r'\bcorrect\b', r'\bvalid\b',
                r'\bagree\b', r'\baffirmative\b'
            ],
            "no_patterns": [
                r'\bno\b', r'\bfalse\b', r'\bincorrect\b', r'\binvalid\b',
                r'\bdisagree\b', r'\bnegative\b'
            ],
            "confidence_patterns": [
                r'(\d+)%', r'(\d+) percent', r'(\d+)/10', r'(\d+) out of 10',
                r'very confident', r'quite confident', r'somewhat confident',
                r'not confident', r'uncertain'
            ],
            "implicature_patterns": [
                r'not all', r'some but not all', r'implies.*not',
                r'suggests.*not', r'means.*not', r'but not'
            ]
        }
    
    def _initialize_implicature_indicators(self) -> Dict[str, List[str]]:
        """Initialize indicators of scalar implicature derivation."""
        return {
            "positive_indicators": [
                "not all", "some but not all", "implies that not",
                "suggests that not", "means that not", "but not",
                "excludes", "rules out", "cannot be", "would have said",
                "stronger term", "more specific", "if it were all"
            ],
            "negative_indicators": [
                "could be all", "might be all", "doesn't exclude",
                "compatible with", "allows for", "possible that all",
                "literal meaning", "just means", "only says"
            ],
            "quantifier_specific": [
                "not everyone", "not everything", "not the whole",
                "partial", "subset", "portion"
            ],
            "adjectival_specific": [
                "not very", "not extremely", "moderate", "mild",
                "somewhat", "rather than", "instead of"
            ]
        }
    
    def _extract_binary_response(self, text: str) -> Optional[bool]:
        """Extract yes/no or true/false response from text."""
        text_lower = text.lower()
        
        # Count yes/true vs no/false patterns
        yes_count = sum(len(re.findall(pattern, text_lower, re.IGNORECASE)) 
                       for pattern in self.patterns["yes_patterns"])
        no_count = sum(len(re.findall(pattern, text_lower, re.IGNORECASE)) 
                      for pattern in self.patterns["no_patterns"])
        
        if yes_count > no_count:
            return True
        elif no_count > yes_count:
            return False
        else:
            return None
    
    def _extract_confidence(self, text: str) -> Optional[float]:
        """Extract confidence score from text."""
        text_lower = text.lower()
        
        # Look for percentage or numerical confidence
        for pattern in self.patterns["confidence_patterns"]:
            matches = re.findall(pattern, text_lower)
            if matches:
                try:
                    if '%' in pattern or 'percent' in pattern:
                        return float(matches[0]) / 100.0
                    elif '/10' in pattern or 'out of 10' in pattern:
                        return float(matches[0]) / 10.0
                except ValueError:
                    continue
        
        # Look for qualitative confidence indicators
        if 'very confident' in text_lower:
            return 0.9
        elif 'quite confident' in text_lower or 'confident' in text_lower:
            return 0.7
        elif 'somewhat confident' in text_lower:
            return 0.5
        elif 'not confident' in text_lower or 'uncertain' in text_lower:
            return 0.3
        
        return None
    
    def _detect_implicature_derivation(self, text: str, scenario: ScalarScenario) -> Optional[bool]:
        """Detect whether scalar implicature was derived from the response."""
        text_lower = text.lower()
        
        # Count positive and negative indicators
        positive_count = 0
        negative_count = 0
        
        # General indicators
        for indicator in self.implicature_indicators["positive_indicators"]:
            if indicator in text_lower:
                positive_count += 1
        
        for indicator in self.implicature_indicators["negative_indicators"]:
            if indicator in text_lower:
                negative_count += 1
        
        # Scale-specific indicators
        if scenario.scale.scale_type == ScaleType.QUANTIFIER:
            for indicator in self.implicature_indicators["quantifier_specific"]:
                if indicator in text_lower:
                    positive_count += 1
        else:
            for indicator in self.implicature_indicators["adjectival_specific"]:
                if indicator in text_lower:
                    positive_count += 1
        
        # Check for explicit mention of the strong term with negation
        strong_term = scenario.scale.strong_term.lower()
        if f"not {strong_term}" in text_lower or f"not all" in text_lower:
            positive_count += 2
        
        # Check for explicit mention that allows the strong term
        if f"could be {strong_term}" in text_lower or f"might be {strong_term}" in text_lower:
            negative_count += 2
        
        # Make decision based on counts
        if positive_count > negative_count:
            return True
        elif negative_count > positive_count:
            return False
        else:
            return None
    
    def _extract_explanation(self, text: str) -> str:
        """Extract the explanation portion of the response."""
        # Remove common prefixes
        prefixes_to_remove = [
            "answer:", "explanation:", "reasoning:", "because:",
            "the reason is:", "this is because:", "my reasoning:"
        ]
        
        cleaned_text = text.strip()
        for prefix in prefixes_to_remove:
            if cleaned_text.lower().startswith(prefix):
                cleaned_text = cleaned_text[len(prefix):].strip()
        
        return cleaned_text
    
    def _extract_completion(self, text: str) -> str:
        """Extract sentence completion from response."""
        # Look for completion after common markers
        completion_markers = ["completion:", "so", "therefore", "thus", "hence"]
        
        text_lower = text.lower()
        for marker in completion_markers:
            if marker in text_lower:
                # Find the position and extract what follows
                pos = text_lower.find(marker)
                if pos != -1:
                    completion = text[pos + len(marker):].strip()
                    # Remove leading punctuation
                    completion = re.sub(r'^[,:\-\s]+', '', completion)
                    return completion
        
        # If no marker found, return the whole response
        return text.strip()
    
    def process_response(self, raw_response: str, scenario: ScalarScenario, 
                        expected_type: ResponseType) -> ProcessedResponse:
        """
        Process a raw model response.
        
        Args:
            raw_response: Raw text response from the model
            scenario: The scenario that generated this response
            expected_type: Expected type of response
            
        Returns:
            ProcessedResponse object
        """
        processed = ProcessedResponse(
            raw_response=raw_response,
            scenario_id=scenario.scenario_id,
            response_type=expected_type
        )
        
        try:
            # Extract binary response if applicable
            if expected_type in [ResponseType.BINARY, ResponseType.IMPLICATURE]:
                processed.binary_answer = self._extract_binary_response(raw_response)
            
            # Always try to detect implicature derivation
            processed.implicature_derived = self._detect_implicature_derivation(raw_response, scenario)
            
            # Extract confidence
            processed.confidence_score = self._extract_confidence(raw_response)
            
            # Extract explanation
            if expected_type == ResponseType.EXPLANATION or len(raw_response) > 50:
                processed.explanation = self._extract_explanation(raw_response)
            
            # Extract completion
            if expected_type == ResponseType.COMPLETION:
                processed.completion = self._extract_completion(raw_response)
            
            # Check processing success
            if expected_type == ResponseType.BINARY and processed.binary_answer is None:
                processed.processing_success = False
                processed.processing_notes = "Could not extract binary answer"
            elif expected_type == ResponseType.IMPLICATURE and processed.implicature_derived is None:
                processed.processing_success = False
                processed.processing_notes = "Could not determine implicature derivation"
            
        except Exception as e:
            processed.processing_success = False
            processed.processing_notes = f"Processing error: {str(e)}"
            logger.error(f"Error processing response: {str(e)}")
        
        return processed
    
    def process_batch_responses(self, responses: List[Tuple[str, ScalarScenario]], 
                               expected_type: ResponseType) -> List[ProcessedResponse]:
        """
        Process multiple responses in batch.
        
        Args:
            responses: List of (raw_response, scenario) tuples
            expected_type: Expected type of responses
            
        Returns:
            List of ProcessedResponse objects
        """
        processed_responses = []
        
        for raw_response, scenario in responses:
            processed = self.process_response(raw_response, scenario, expected_type)
            processed_responses.append(processed)
        
        return processed_responses
    
    def get_processing_statistics(self, processed_responses: List[ProcessedResponse]) -> Dict[str, Any]:
        """Get statistics about response processing success."""
        total = len(processed_responses)
        if total == 0:
            return {}
        
        successful = sum(1 for r in processed_responses if r.processing_success)
        binary_extracted = sum(1 for r in processed_responses if r.binary_answer is not None)
        implicature_detected = sum(1 for r in processed_responses if r.implicature_derived is not None)
        confidence_extracted = sum(1 for r in processed_responses if r.confidence_score is not None)
        
        return {
            "total_responses": total,
            "processing_success_rate": successful / total,
            "binary_extraction_rate": binary_extracted / total,
            "implicature_detection_rate": implicature_detected / total,
            "confidence_extraction_rate": confidence_extracted / total,
            "failed_responses": [r.processing_notes for r in processed_responses if not r.processing_success]
        }
