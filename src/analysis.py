"""
Analysis and visualization for scalar implicature experiments.
"""

import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
from typing import Dict, List, Any, Optional, Tuple
import json
import os
from scipy import stats
from collections import defaultdict

from data.scenarios import ScaleType, ContextType, NegationType
from data.human_patterns import HumanResponse
from src.response_processor import ProcessedResponse

class ExperimentAnalyzer:
    """Analyzer for scalar implicature experiment results."""
    
    def __init__(self, results_file: str = None, results_data: Dict[str, Any] = None):
        """
        Initialize analyzer with results data.
        
        Args:
            results_file: Path to results JSON file
            results_data: Direct results data dictionary
        """
        if results_file:
            with open(results_file, 'r') as f:
                self.results = json.load(f)
        elif results_data:
            self.results = results_data
        else:
            raise ValueError("Must provide either results_file or results_data")
        
        self.scenarios_df = self._create_scenarios_dataframe()
        self.model_df = self._create_model_dataframe()
        self.human_df = self._create_human_dataframe()
        
    def _create_scenarios_dataframe(self) -> pd.DataFrame:
        """Create DataFrame from scenarios data."""
        scenarios_data = self.results.get('scenarios', [])
        if not scenarios_data:
            return pd.DataFrame()
        
        df = pd.DataFrame(scenarios_data)
        return df
    
    def _create_model_dataframe(self) -> pd.DataFrame:
        """Create DataFrame from model responses."""
        model_data = self.results.get('model_responses', [])
        if not model_data:
            return pd.DataFrame()
        
        df = pd.DataFrame(model_data)
        
        # Merge with scenario information
        if not self.scenarios_df.empty:
            df = df.merge(self.scenarios_df, on='scenario_id', how='left')
        
        return df
    
    def _create_human_dataframe(self) -> pd.DataFrame:
        """Create DataFrame from human responses."""
        human_data = self.results.get('human_responses', [])
        if not human_data:
            return pd.DataFrame()
        
        df = pd.DataFrame(human_data)
        
        # Merge with scenario information
        if not self.scenarios_df.empty:
            df = df.merge(self.scenarios_df, on='scenario_id', how='left')
        
        return df
    
    def get_summary_statistics(self) -> Dict[str, Any]:
        """Get summary statistics for the experiment."""
        summary = {
            'total_scenarios': len(self.scenarios_df),
            'total_model_responses': len(self.model_df),
            'total_human_responses': len(self.human_df),
            'model_processing_success_rate': 0.0,
            'model_implicature_rate': 0.0,
            'human_implicature_rate': 0.0,
            'scale_type_distribution': {},
            'context_type_distribution': {},
            'negation_type_distribution': {}
        }
        
        # Model statistics
        if not self.model_df.empty:
            summary['model_processing_success_rate'] = self.model_df['processing_success'].mean()
            valid_model = self.model_df[self.model_df['implicature_derived'].notna()]
            if not valid_model.empty:
                summary['model_implicature_rate'] = valid_model['implicature_derived'].mean()
        
        # Human statistics
        if not self.human_df.empty:
            summary['human_implicature_rate'] = self.human_df['implicature_derived'].mean()
        
        # Distribution statistics
        if not self.scenarios_df.empty:
            summary['scale_type_distribution'] = self.scenarios_df['scale_type'].value_counts().to_dict()
            summary['context_type_distribution'] = self.scenarios_df['context_type'].value_counts().to_dict()
            summary['negation_type_distribution'] = self.scenarios_df['negation_type'].value_counts().to_dict()
        
        return summary
    
    def analyze_by_condition(self) -> Dict[str, pd.DataFrame]:
        """Analyze results by experimental conditions."""
        analyses = {}
        
        if self.model_df.empty:
            return analyses
        
        # Analysis by scale type
        if 'scale_type' in self.model_df.columns:
            scale_analysis = self.model_df.groupby('scale_type').agg({
                'implicature_derived': ['count', 'sum', 'mean'],
                'processing_success': 'mean',
                'confidence_score': 'mean'
            }).round(3)
            analyses['by_scale_type'] = scale_analysis
        
        # Analysis by context type
        if 'context_type' in self.model_df.columns:
            context_analysis = self.model_df.groupby('context_type').agg({
                'implicature_derived': ['count', 'sum', 'mean'],
                'processing_success': 'mean',
                'confidence_score': 'mean'
            }).round(3)
            analyses['by_context_type'] = context_analysis
        
        # Analysis by negation type
        if 'negation_type' in self.model_df.columns:
            negation_analysis = self.model_df.groupby('negation_type').agg({
                'implicature_derived': ['count', 'sum', 'mean'],
                'processing_success': 'mean',
                'confidence_score': 'mean'
            }).round(3)
            analyses['by_negation_type'] = negation_analysis
        
        # Combined analysis
        if all(col in self.model_df.columns for col in ['scale_type', 'context_type']):
            combined_analysis = self.model_df.groupby(['scale_type', 'context_type']).agg({
                'implicature_derived': ['count', 'mean'],
                'processing_success': 'mean'
            }).round(3)
            analyses['by_scale_and_context'] = combined_analysis
        
        return analyses
    
    def compare_model_human(self) -> Dict[str, Any]:
        """Compare model and human responses."""
        if self.model_df.empty or self.human_df.empty:
            return {"error": "Insufficient data for comparison"}
        
        comparison = {}
        
        # Overall comparison
        model_rate = self.model_df[self.model_df['implicature_derived'].notna()]['implicature_derived'].mean()
        human_rate = self.human_df['implicature_derived'].mean()
        
        comparison['overall'] = {
            'model_implicature_rate': model_rate,
            'human_implicature_rate': human_rate,
            'difference': abs(model_rate - human_rate),
            'model_higher': model_rate > human_rate
        }
        
        # Comparison by conditions
        for condition in ['scale_type', 'context_type', 'negation_type']:
            if condition in self.model_df.columns and condition in self.human_df.columns:
                model_by_condition = self.model_df.groupby(condition)['implicature_derived'].mean()
                human_by_condition = self.human_df.groupby(condition)['implicature_derived'].mean()
                
                condition_comparison = {}
                for cond_value in model_by_condition.index:
                    if cond_value in human_by_condition.index:
                        model_val = model_by_condition[cond_value]
                        human_val = human_by_condition[cond_value]
                        condition_comparison[cond_value] = {
                            'model': model_val,
                            'human': human_val,
                            'difference': abs(model_val - human_val)
                        }
                
                comparison[f'by_{condition}'] = condition_comparison
        
        return comparison
    
    def create_visualizations(self, output_dir: str = "results/plots") -> List[str]:
        """Create visualization plots."""
        os.makedirs(output_dir, exist_ok=True)
        plot_files = []
        
        # Set style
        plt.style.use('default')
        sns.set_palette("husl")
        
        # 1. Overall implicature rates
        if not self.model_df.empty:
            fig, ax = plt.subplots(figsize=(10, 6))
            
            # Model data
            model_valid = self.model_df[self.model_df['implicature_derived'].notna()]
            if not model_valid.empty:
                model_rate = model_valid['implicature_derived'].mean()
                ax.bar(['Model'], [model_rate], alpha=0.7, label='Model')
            
            # Human data
            if not self.human_df.empty:
                human_rate = self.human_df['implicature_derived'].mean()
                ax.bar(['Human'], [human_rate], alpha=0.7, label='Human')
            
            ax.set_ylabel('Implicature Derivation Rate')
            ax.set_title('Overall Implicature Derivation Rates')
            ax.set_ylim(0, 1)
            ax.legend()
            
            plot_file = os.path.join(output_dir, 'overall_rates.png')
            plt.savefig(plot_file, dpi=300, bbox_inches='tight')
            plt.close()
            plot_files.append(plot_file)
        
        # 2. Rates by scale type
        if 'scale_type' in self.model_df.columns:
            fig, ax = plt.subplots(figsize=(12, 6))
            
            # Model data by scale type
            model_by_scale = self.model_df.groupby('scale_type')['implicature_derived'].mean()
            x_pos = np.arange(len(model_by_scale))
            
            bars1 = ax.bar(x_pos - 0.2, model_by_scale.values, 0.4, 
                          label='Model', alpha=0.7)
            
            # Human data by scale type
            if 'scale_type' in self.human_df.columns:
                human_by_scale = self.human_df.groupby('scale_type')['implicature_derived'].mean()
                bars2 = ax.bar(x_pos + 0.2, human_by_scale.values, 0.4, 
                              label='Human', alpha=0.7)
            
            ax.set_xlabel('Scale Type')
            ax.set_ylabel('Implicature Derivation Rate')
            ax.set_title('Implicature Rates by Scale Type')
            ax.set_xticks(x_pos)
            ax.set_xticklabels(model_by_scale.index)
            ax.set_ylim(0, 1)
            ax.legend()
            
            plot_file = os.path.join(output_dir, 'rates_by_scale_type.png')
            plt.savefig(plot_file, dpi=300, bbox_inches='tight')
            plt.close()
            plot_files.append(plot_file)
        
        # 3. Rates by context type
        if 'context_type' in self.model_df.columns:
            fig, ax = plt.subplots(figsize=(12, 6))
            
            model_by_context = self.model_df.groupby('context_type')['implicature_derived'].mean()
            x_pos = np.arange(len(model_by_context))
            
            bars1 = ax.bar(x_pos - 0.2, model_by_context.values, 0.4, 
                          label='Model', alpha=0.7)
            
            if 'context_type' in self.human_df.columns:
                human_by_context = self.human_df.groupby('context_type')['implicature_derived'].mean()
                bars2 = ax.bar(x_pos + 0.2, human_by_context.values, 0.4, 
                              label='Human', alpha=0.7)
            
            ax.set_xlabel('Context Type')
            ax.set_ylabel('Implicature Derivation Rate')
            ax.set_title('Implicature Rates by Context Type')
            ax.set_xticks(x_pos)
            ax.set_xticklabels(model_by_context.index)
            ax.set_ylim(0, 1)
            ax.legend()
            
            plot_file = os.path.join(output_dir, 'rates_by_context_type.png')
            plt.savefig(plot_file, dpi=300, bbox_inches='tight')
            plt.close()
            plot_files.append(plot_file)
        
        # 4. Heatmap of conditions
        if all(col in self.model_df.columns for col in ['scale_type', 'context_type']):
            pivot_data = self.model_df.pivot_table(
                values='implicature_derived', 
                index='scale_type', 
                columns='context_type', 
                aggfunc='mean'
            )
            
            if not pivot_data.empty:
                fig, ax = plt.subplots(figsize=(10, 6))
                sns.heatmap(pivot_data, annot=True, cmap='viridis', 
                           vmin=0, vmax=1, ax=ax)
                ax.set_title('Implicature Rates by Scale Type and Context')
                
                plot_file = os.path.join(output_dir, 'heatmap_conditions.png')
                plt.savefig(plot_file, dpi=300, bbox_inches='tight')
                plt.close()
                plot_files.append(plot_file)
        
        return plot_files
    
    def generate_report(self, output_file: str = "results/analysis_report.txt") -> str:
        """Generate a text report of the analysis."""
        os.makedirs(os.path.dirname(output_file), exist_ok=True)
        
        with open(output_file, 'w') as f:
            f.write("SCALAR IMPLICATURE EXPERIMENT ANALYSIS REPORT\n")
            f.write("=" * 50 + "\n\n")
            
            # Summary statistics
            summary = self.get_summary_statistics()
            f.write("SUMMARY STATISTICS\n")
            f.write("-" * 20 + "\n")
            for key, value in summary.items():
                f.write(f"{key}: {value}\n")
            f.write("\n")
            
            # Condition analysis
            condition_analyses = self.analyze_by_condition()
            f.write("ANALYSIS BY CONDITIONS\n")
            f.write("-" * 25 + "\n")
            for condition, analysis in condition_analyses.items():
                f.write(f"\n{condition.upper()}:\n")
                f.write(str(analysis))
                f.write("\n")
            
            # Model-human comparison
            if not self.human_df.empty:
                comparison = self.compare_model_human()
                f.write("\nMODEL-HUMAN COMPARISON\n")
                f.write("-" * 25 + "\n")
                for key, value in comparison.items():
                    f.write(f"{key}: {value}\n")
            
            # Metrics from original results
            if 'metrics' in self.results:
                f.write("\nEVALUATION METRICS\n")
                f.write("-" * 20 + "\n")
                metrics = self.results['metrics']
                for metric_name, metric_data in metrics.get('metrics', {}).items():
                    f.write(f"{metric_name}: {metric_data.get('value', 'N/A')}\n")
                    if 'description' in metric_data:
                        f.write(f"  Description: {metric_data['description']}\n")
        
        return output_file
