# Scalar Implicature Experiment Reproduction

This project implements a complete reproduction of the scalar implicature experiment described in the paper "Linking language model predictions to human behaviour on scalar implicatures" by <PERSON><PERSON><PERSON> et al. (2024).

## Overview

Scalar implicatures are pragmatic inferences that go beyond the literal meaning of utterances. For example, when someone says "Some students passed the exam," we typically infer that not all students passed, even though this is not explicitly stated.

This implementation provides:
- Comprehensive experimental framework for testing scalar implicature behavior
- Integration with local language models (specifically Qwen3-4B)
- Simulated human behavioral patterns for comparison
- Statistical analysis and visualization tools
- Modular design for easy extension and customization

## Installation

1. Clone this repository
2. Install dependencies:
```bash
pip install -r requirements.txt
```

3. Configure your model path in `config/model_config.py`:
```python
# Update the model_path to point to your local Qwen3-4B model
model_path: str = "/path/to/your/Qwen3-4B"
```

## Quick Start

### 1. Check Setup
```bash
python run_experiment.py --check
```

### 2. Run Quick Test
```bash
python run_experiment.py --quick
```

### 3. Run Basic Experiment
```bash
python run_experiment.py --basic
```

## Available Experiments

### Command Line Interface

The main script `run_experiment.py` provides several experiment types:

```bash
# Check experimental setup
python run_experiment.py --check

# Quick test (3 scenarios)
python run_experiment.py --quick

# Basic scalar implicature experiment (20 scenarios)
python run_experiment.py --basic

# Adjectival scales experiment (50 scenarios)
python run_experiment.py --adjectival

# Negation effects experiment (40 scenarios)
python run_experiment.py --negation

# Comprehensive analysis (100 scenarios)
python run_experiment.py --comprehensive

# Custom experiment
python run_experiment.py --custom --scenarios 30 --name my_test
```

### Individual Experiment Scripts

You can also run experiments directly:

```bash
# Basic experiment
python experiments/basic_scalar.py

# Quick test only
python experiments/basic_scalar.py --quick

# Adjectival scales
python experiments/adjectival_scales.py

# Negation effects
python experiments/negation_effects.py

# Comprehensive analysis
python experiments/comparative_analysis.py
```

## Programmatic Usage

### Basic Usage

```python
from src.experiment_runner import ExperimentRunner
from config.experiment_config import get_experiment_config

# Configure experiment
config = get_experiment_config({
    'experiment_name': 'my_experiment',
    'total_scenarios': 50,
    'output_directory': 'results/my_experiment'
})

runner = ExperimentRunner(config)

# Run complete experiment
results_file = runner.run_complete_experiment()
```

### Quick Test

```python
# Run a quick test with 5 scenarios
runner = ExperimentRunner()
results = runner.run_quick_test(num_scenarios=5)
print(f"Model status: {results['model_info']['status']}")
```

### Custom Analysis

```python
from src.analysis import ExperimentAnalyzer

# Load and analyze results
analyzer = ExperimentAnalyzer("results/experiment_results.json")

# Get summary statistics
summary = analyzer.get_summary_statistics()

# Create visualizations
plot_files = analyzer.create_visualizations("results/plots")

# Generate report
report_file = analyzer.generate_report("results/analysis_report.txt")
```

## Configuration

The experiment can be configured through the configuration files:

### Model Configuration (`config/model_config.py`)
```python
@dataclass
class ModelConfig:
    model_path: str = "/Users/<USER>/VibeCoding/Models/Qwen3-4B"
    model_name: str = "Qwen3-4B"
    device: str = "auto"
    max_length: int = 512
    temperature: float = 0.7
    batch_size: int = 4
```

### Experiment Configuration (`config/experiment_config.py`)
```python
@dataclass
class ExperimentConfig:
    experiment_name: str = "scalar_implicature_experiment"
    total_scenarios: int = 50
    samples_per_condition: int = 10
    test_quantifier_scales: bool = True
    test_adjectival_scales: bool = True
    test_negation_effects: bool = True
    test_context_variations: bool = True
    output_directory: str = "results"
```

## Interactive Analysis

Use the Jupyter notebook for interactive exploration:

```bash
jupyter notebook notebooks/experiment_exploration.ipynb
```

The notebook provides:
- Interactive data exploration
- Custom visualizations
- Statistical analysis
- Ability to run quick experiments

## Project Structure

```
scalar_implicature_experiment/
├── config/                 # Configuration files
│   ├── experiment_config.py
│   └── model_config.py
├── data/                   # Data generation and scenarios
│   ├── scenarios.py
│   └── human_patterns.py
├── src/                    # Core implementation
│   ├── model_interface.py
│   ├── prompt_templates.py
│   ├── response_processor.py
│   ├── metrics.py
│   ├── analysis.py
│   └── experiment_runner.py
├── experiments/            # Specific experiment scripts
│   ├── basic_scalar.py
│   ├── adjectival_scales.py
│   ├── negation_effects.py
│   └── comparative_analysis.py
├── notebooks/              # Jupyter notebooks for analysis
│   └── experiment_exploration.ipynb
├── results/                # Experimental results (created during runs)
├── run_experiment.py       # Main command-line interface
└── requirements.txt        # Dependencies
```

## Results and Output

Each experiment generates:
- **JSON results file**: Complete experimental data
- **Analysis report**: Text summary of findings
- **Visualizations**: Plots and charts in PNG format
- **Logs**: Detailed execution logs

Example output structure:
```
results/
├── basic_scalar/
│   ├── experiment_results_20240801_143022.json
│   ├── basic_analysis_report.txt
│   ├── plots/
│   │   ├── overall_rates.png
│   │   ├── rates_by_scale_type.png
│   │   └── heatmap_conditions.png
│   └── experiment.log
└── comprehensive/
    ├── experiment_results_20240801_150045.json
    ├── comprehensive_analysis_report.txt
    └── plots/
        ├── model_human_correlation.png
        ├── processing_success_by_condition.png
        └── confidence_distribution.png
```

## Troubleshooting

### Common Issues

1. **Model not found**: Check the model path in `config/model_config.py`
2. **CUDA out of memory**: Reduce batch size in model config
3. **Import errors**: Ensure all dependencies are installed with `pip install -r requirements.txt`

### Debug Mode

Run with verbose logging:
```bash
python run_experiment.py --basic --verbose
```

### Quick Diagnostics

```bash
# Check if everything is set up correctly
python run_experiment.py --check

# Test with minimal scenarios
python run_experiment.py --quick
```

## Citation

If you use this code, please cite the original paper:

```bibtex
@inproceedings{zinova2024linking,
  title={Linking language model predictions to human behaviour on scalar implicatures},
  author={Zinova, Yulia and others},
  booktitle={Proceedings of the Workshop on Neurosymbolic AI for Language and Reasoning},
  year={2024}
}
```
