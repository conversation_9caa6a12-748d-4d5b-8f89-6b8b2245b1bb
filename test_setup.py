#!/usr/bin/env python3
"""
Test script to verify the scalar implicature experiment setup.
"""

import sys
import os
import traceback

def test_imports():
    """Test that all required modules can be imported."""
    print("Testing imports...")
    
    try:
        # Test standard library imports
        import json
        import logging
        from datetime import datetime
        from typing import Dict, List, Any, Optional
        from dataclasses import dataclass
        print("✓ Standard library imports successful")
        
        # Test scientific computing imports
        import numpy as np
        import pandas as pd
        import matplotlib.pyplot as plt
        import seaborn as sns
        from scipy import stats
        print("✓ Scientific computing imports successful")
        
        # Test ML/NLP imports
        import torch
        import transformers
        print("✓ PyTorch and Transformers imports successful")
        
        # Test project imports
        from config.experiment_config import ExperimentConfig, get_experiment_config
        from config.model_config import ModelConfig, get_model_config
        from data.scenarios import ScenarioGenerator, ScalarScenario
        from data.human_patterns import HumanPatternSimulator
        from src.model_interface import ModelInterface
        from src.prompt_templates import PromptTemplate, PromptType
        from src.response_processor import ResponseProcessor
        from src.metrics import ScalarImplicatureMetrics
        from src.analysis import ExperimentAnalyzer
        from src.experiment_runner import ExperimentRunner
        print("✓ Project imports successful")
        
        return True
        
    except ImportError as e:
        print(f"✗ Import error: {e}")
        return False
    except Exception as e:
        print(f"✗ Unexpected error during imports: {e}")
        return False

def test_configuration():
    """Test configuration loading."""
    print("\nTesting configuration...")
    
    try:
        # Test experiment config
        exp_config = get_experiment_config()
        print(f"✓ Experiment config loaded: {exp_config.experiment_name}")
        
        # Test model config
        model_config = get_model_config()
        print(f"✓ Model config loaded: {model_config.model_name}")
        print(f"  Model path: {model_config.model_path}")
        
        # Check if model path exists
        if os.path.exists(model_config.model_path):
            print("✓ Model path exists")
        else:
            print("⚠ Model path does not exist - you may need to update config/model_config.py")
        
        return True
        
    except Exception as e:
        print(f"✗ Configuration error: {e}")
        return False

def test_scenario_generation():
    """Test scenario generation."""
    print("\nTesting scenario generation...")
    
    try:
        generator = ScenarioGenerator()
        
        # Test scale generation
        quantifier_scales = generator.get_scales_by_type(generator.ScaleType.QUANTIFIER)
        adjectival_scales = generator.get_scales_by_type(generator.ScaleType.ADJECTIVAL)
        
        print(f"✓ Generated {len(quantifier_scales)} quantifier scales")
        print(f"✓ Generated {len(adjectival_scales)} adjectival scales")
        
        # Test scenario generation
        scenarios = generator.generate_scenario_set(num_scenarios=5)
        print(f"✓ Generated {len(scenarios)} test scenarios")
        
        # Show example scenario
        if scenarios:
            example = scenarios[0]
            print(f"  Example: {example.target_sentence}")
        
        return True
        
    except Exception as e:
        print(f"✗ Scenario generation error: {e}")
        traceback.print_exc()
        return False

def test_human_simulation():
    """Test human pattern simulation."""
    print("\nTesting human pattern simulation...")
    
    try:
        from data.scenarios import ScenarioGenerator
        
        # Generate a few scenarios
        generator = ScenarioGenerator()
        scenarios = generator.generate_scenario_set(num_scenarios=3)
        
        # Test human simulation
        simulator = HumanPatternSimulator()
        human_responses = simulator.simulate_experiment(scenarios, num_participants=5)
        
        print(f"✓ Generated {len(human_responses)} human responses")
        
        # Show example response
        if human_responses:
            example = human_responses[0]
            print(f"  Example: Participant {example.participant_id}, Implicature: {example.implicature_derived}")
        
        return True
        
    except Exception as e:
        print(f"✗ Human simulation error: {e}")
        return False

def test_prompt_templates():
    """Test prompt template generation."""
    print("\nTesting prompt templates...")
    
    try:
        from data.scenarios import ScenarioGenerator
        from src.prompt_templates import AdaptivePromptTemplate, PromptType
        
        # Generate a test scenario
        generator = ScenarioGenerator()
        scenarios = generator.generate_scenario_set(num_scenarios=1)
        
        if not scenarios:
            print("✗ No scenarios generated for prompt testing")
            return False
        
        scenario = scenarios[0]
        template = AdaptivePromptTemplate()
        
        # Test different prompt types
        for prompt_type in [PromptType.INFERENCE, PromptType.TRUTH_VALUE, PromptType.COMPLETION]:
            prompt = template.generate_adaptive_prompt(scenario, prompt_type)
            print(f"✓ Generated {prompt_type.value} prompt ({len(prompt)} chars)")
        
        return True
        
    except Exception as e:
        print(f"✗ Prompt template error: {e}")
        return False

def test_directories():
    """Test that required directories can be created."""
    print("\nTesting directory creation...")
    
    try:
        # Test results directory
        results_dir = "results/test_setup"
        os.makedirs(results_dir, exist_ok=True)
        print(f"✓ Created results directory: {results_dir}")
        
        # Test plots directory
        plots_dir = os.path.join(results_dir, "plots")
        os.makedirs(plots_dir, exist_ok=True)
        print(f"✓ Created plots directory: {plots_dir}")
        
        return True
        
    except Exception as e:
        print(f"✗ Directory creation error: {e}")
        return False

def main():
    """Run all setup tests."""
    print("Scalar Implicature Experiment Setup Test")
    print("=" * 45)
    
    tests = [
        ("Import Test", test_imports),
        ("Configuration Test", test_configuration),
        ("Scenario Generation Test", test_scenario_generation),
        ("Human Simulation Test", test_human_simulation),
        ("Prompt Template Test", test_prompt_templates),
        ("Directory Test", test_directories)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        try:
            if test_func():
                passed += 1
            else:
                print(f"✗ {test_name} failed")
        except Exception as e:
            print(f"✗ {test_name} failed with exception: {e}")
    
    print(f"\nTest Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! Your setup is ready.")
        print("\nNext steps:")
        print("1. Run a quick test: python run_experiment.py --quick")
        print("2. Run basic experiment: python run_experiment.py --basic")
        return True
    else:
        print("❌ Some tests failed. Please check the errors above.")
        print("\nCommon fixes:")
        print("1. Install dependencies: pip install -r requirements.txt")
        print("2. Update model path in config/model_config.py")
        print("3. Check Python version (3.8+ recommended)")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
