#!/usr/bin/env python3
"""
Main script for running scalar implicature experiments.

This script provides a command-line interface for running different types of
scalar implicature experiments with the local Qwen model.
"""

import sys
import os
import argparse
import logging
from typing import Optional

# Add project root to path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from config.experiment_config import get_experiment_config
from config.model_config import get_model_config
from src.experiment_runner import ExperimentRunner
from src.prompt_templates import PromptType

def setup_logging(level: str = "INFO"):
    """Setup logging configuration."""
    log_level = getattr(logging, level.upper())
    logging.basicConfig(
        level=log_level,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )

def run_quick_test():
    """Run a quick test to verify the setup."""
    print("Running Quick Test")
    print("=" * 20)
    
    try:
        exp_config = get_experiment_config({
            'experiment_name': 'quick_test',
            'total_scenarios': 5,
            'output_directory': 'results/quick_test'
        })
        
        runner = ExperimentRunner(exp_config)
        results = runner.run_quick_test(num_scenarios=3)
        
        print("✓ Quick test completed successfully!")
        print(f"✓ Generated {results['num_scenarios_generated']} scenarios")
        print(f"✓ Model status: {results['model_info']['status']}")
        
        print("\nSample results:")
        for i, result in enumerate(results['test_results'][:2]):
            print(f"\nTest {i+1}:")
            print(f"  Sentence: {result['target_sentence']}")
            print(f"  Implicature: {result['implicature_derived']}")
            print(f"  Success: {result['processing_success']}")
        
        return True
        
    except Exception as e:
        print(f"✗ Quick test failed: {str(e)}")
        return False

def run_basic_experiment():
    """Run the basic scalar implicature experiment."""
    print("Running Basic Experiment")
    print("=" * 25)
    
    try:
        from experiments.basic_scalar import run_basic_experiment
        return run_basic_experiment()
    except Exception as e:
        print(f"✗ Basic experiment failed: {str(e)}")
        return False

def run_adjectival_experiment():
    """Run the adjectival scales experiment."""
    print("Running Adjectival Scales Experiment")
    print("=" * 35)
    
    try:
        from experiments.adjectival_scales import run_adjectival_experiment
        return run_adjectival_experiment()
    except Exception as e:
        print(f"✗ Adjectival experiment failed: {str(e)}")
        return False

def run_negation_experiment():
    """Run the negation effects experiment."""
    print("Running Negation Effects Experiment")
    print("=" * 35)
    
    try:
        from experiments.negation_effects import run_negation_experiment
        return run_negation_experiment()
    except Exception as e:
        print(f"✗ Negation experiment failed: {str(e)}")
        return False

def run_comprehensive_experiment():
    """Run the comprehensive comparative analysis."""
    print("Running Comprehensive Analysis")
    print("=" * 30)
    
    try:
        from experiments.comparative_analysis import run_comparative_analysis
        return run_comparative_analysis()
    except Exception as e:
        print(f"✗ Comprehensive analysis failed: {str(e)}")
        return False

def run_custom_experiment(config_overrides: dict):
    """Run a custom experiment with specified configuration."""
    print("Running Custom Experiment")
    print("=" * 25)
    
    try:
        exp_config = get_experiment_config(config_overrides)
        runner = ExperimentRunner(exp_config)
        
        results_file = runner.run_complete_experiment()
        print(f"✓ Custom experiment completed!")
        print(f"✓ Results saved to: {results_file}")
        
        return True
        
    except Exception as e:
        print(f"✗ Custom experiment failed: {str(e)}")
        return False

def check_setup():
    """Check if the experimental setup is correct."""
    print("Checking Experimental Setup")
    print("=" * 30)
    
    # Check model path
    model_config = get_model_config()
    model_path = model_config.model_path
    
    print(f"Model path: {model_path}")
    if os.path.exists(model_path):
        print("✓ Model path exists")
    else:
        print("✗ Model path does not exist")
        print("  Please check the model path in config/model_config.py")
        return False
    
    # Check dependencies
    try:
        import torch
        import transformers
        print("✓ PyTorch and Transformers available")
    except ImportError as e:
        print(f"✗ Missing dependencies: {e}")
        print("  Please install requirements: pip install -r requirements.txt")
        return False
    
    # Check output directory
    exp_config = get_experiment_config()
    output_dir = exp_config.output_directory
    os.makedirs(output_dir, exist_ok=True)
    print(f"✓ Output directory ready: {output_dir}")
    
    print("\n✓ Setup check completed successfully!")
    return True

def main():
    """Main function with command-line interface."""
    parser = argparse.ArgumentParser(
        description="Run scalar implicature experiments with local language models",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  python run_experiment.py --check          # Check setup
  python run_experiment.py --quick          # Quick test
  python run_experiment.py --basic          # Basic experiment
  python run_experiment.py --adjectival     # Adjectival scales
  python run_experiment.py --negation       # Negation effects
  python run_experiment.py --comprehensive  # Full analysis
  python run_experiment.py --custom --scenarios 20 --name my_test
        """
    )
    
    # Experiment type options
    parser.add_argument('--check', action='store_true',
                       help='Check experimental setup')
    parser.add_argument('--quick', action='store_true',
                       help='Run quick test (3 scenarios)')
    parser.add_argument('--basic', action='store_true',
                       help='Run basic scalar implicature experiment')
    parser.add_argument('--adjectival', action='store_true',
                       help='Run adjectival scales experiment')
    parser.add_argument('--negation', action='store_true',
                       help='Run negation effects experiment')
    parser.add_argument('--comprehensive', action='store_true',
                       help='Run comprehensive comparative analysis')
    parser.add_argument('--custom', action='store_true',
                       help='Run custom experiment with specified parameters')
    
    # Custom experiment options
    parser.add_argument('--scenarios', type=int, default=50,
                       help='Number of scenarios for custom experiment')
    parser.add_argument('--name', type=str, default='custom_experiment',
                       help='Name for custom experiment')
    parser.add_argument('--output-dir', type=str,
                       help='Output directory for results')
    
    # General options
    parser.add_argument('--log-level', choices=['DEBUG', 'INFO', 'WARNING', 'ERROR'],
                       default='INFO', help='Logging level')
    parser.add_argument('--verbose', '-v', action='store_true',
                       help='Verbose output')
    
    args = parser.parse_args()
    
    # Setup logging
    log_level = 'DEBUG' if args.verbose else args.log_level
    setup_logging(log_level)
    
    # Count selected options
    experiment_options = [args.check, args.quick, args.basic, args.adjectival, 
                         args.negation, args.comprehensive, args.custom]
    selected_count = sum(experiment_options)
    
    if selected_count == 0:
        print("No experiment type specified. Use --help for options.")
        print("Suggestion: Start with --check to verify setup, then --quick for a test.")
        return 1
    
    if selected_count > 1:
        print("Please specify only one experiment type at a time.")
        return 1
    
    # Run selected experiment
    success = False
    
    try:
        if args.check:
            success = check_setup()
        
        elif args.quick:
            success = run_quick_test()
        
        elif args.basic:
            success = run_basic_experiment()
        
        elif args.adjectival:
            success = run_adjectival_experiment()
        
        elif args.negation:
            success = run_negation_experiment()
        
        elif args.comprehensive:
            success = run_comprehensive_experiment()
        
        elif args.custom:
            config_overrides = {
                'experiment_name': args.name,
                'total_scenarios': args.scenarios
            }
            if args.output_dir:
                config_overrides['output_directory'] = args.output_dir
            
            success = run_custom_experiment(config_overrides)
        
        if success:
            print("\n🎉 Experiment completed successfully!")
            if not args.check and not args.quick:
                print("📊 Check the results directory for detailed analysis and plots.")
                print("📓 Use the Jupyter notebook in notebooks/ for interactive exploration.")
        else:
            print("\n❌ Experiment failed. Check the logs for details.")
    
    except KeyboardInterrupt:
        print("\n⏹️  Experiment interrupted by user.")
        success = False
    
    except Exception as e:
        print(f"\n💥 Unexpected error: {str(e)}")
        if args.verbose:
            import traceback
            traceback.print_exc()
        success = False
    
    return 0 if success else 1

if __name__ == "__main__":
    sys.exit(main())
