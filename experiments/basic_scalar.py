#!/usr/bin/env python3
"""
Basic scalar implicature experiment.
Tests fundamental scalar implicature patterns with quantifiers and adjectives.
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

import logging
from typing import List
from config.experiment_config import get_experiment_config
from config.model_config import get_model_config
from data.scenarios import ScenarioGenerator, ScalarScenario, ScaleType, ContextType, NegationType
from src.experiment_runner import ExperimentRunner
from src.prompt_templates import PromptType
from src.analysis import ExperimentAnalyzer

logger = logging.getLogger(__name__)

def create_basic_scenarios() -> List[ScalarScenario]:
    """Create scenarios for basic scalar implicature testing."""
    generator = ScenarioGenerator()
    
    # Get basic scales
    quantifier_scales = generator.get_scales_by_type(ScaleType.QUANTIFIER)
    adjectival_scales = generator.get_scales_by_type(ScaleType.ADJECTIVAL)
    
    scenarios = []
    
    # Test each scale in simple contexts without negation
    for scale in quantifier_scales[:2]:  # Test first 2 quantifier scales
        scenario = generator.generate_scenario(scale, ContextType.SIMPLE, NegationType.NONE)
        scenarios.append(scenario)
    
    for scale in adjectival_scales[:3]:  # Test first 3 adjectival scales
        scenario = generator.generate_scenario(scale, ContextType.SIMPLE, NegationType.NONE)
        scenarios.append(scenario)
    
    # Add some embedded contexts
    for scale in quantifier_scales[:1]:
        scenario = generator.generate_scenario(scale, ContextType.EMBEDDED, NegationType.NONE)
        scenarios.append(scenario)
    
    for scale in adjectival_scales[:1]:
        scenario = generator.generate_scenario(scale, ContextType.EMBEDDED, NegationType.NONE)
        scenarios.append(scenario)
    
    return scenarios

def run_basic_experiment():
    """Run the basic scalar implicature experiment."""
    print("Starting Basic Scalar Implicature Experiment")
    print("=" * 50)
    
    # Configure experiment for basic testing
    exp_config = get_experiment_config({
        'experiment_name': 'basic_scalar_implicature',
        'total_scenarios': 20,
        'samples_per_condition': 10,
        'test_quantifier_scales': True,
        'test_adjectival_scales': True,
        'test_negation_effects': False,  # Skip negation for basic test
        'test_context_variations': True,
        'output_directory': 'results/basic_scalar'
    })
    
    # Create experiment runner
    runner = ExperimentRunner(exp_config)
    
    try:
        # Generate basic scenarios
        print("Generating basic scenarios...")
        scenarios = create_basic_scenarios()
        print(f"Created {len(scenarios)} scenarios")
        
        # Run model experiment
        print("Running model experiment...")
        model_responses = runner.run_model_experiment(scenarios, PromptType.INFERENCE)
        
        # Simulate human responses
        print("Simulating human responses...")
        human_responses = runner.simulate_human_experiment(scenarios, num_participants=30)
        
        # Calculate metrics
        print("Calculating metrics...")
        metrics = runner.calculate_metrics(model_responses, scenarios, human_responses)
        
        # Save results
        print("Saving results...")
        results_file = runner.save_results(scenarios, model_responses, human_responses, metrics)
        
        # Generate analysis
        print("Generating analysis...")
        analyzer = ExperimentAnalyzer(results_file)
        
        # Create visualizations
        plot_files = analyzer.create_visualizations("results/basic_scalar/plots")
        print(f"Created {len(plot_files)} plots")
        
        # Generate report
        report_file = analyzer.generate_report("results/basic_scalar/basic_analysis_report.txt")
        print(f"Analysis report saved to: {report_file}")
        
        # Print summary
        summary = analyzer.get_summary_statistics()
        print("\nEXPERIMENT SUMMARY:")
        print(f"Model implicature rate: {summary.get('model_implicature_rate', 0):.3f}")
        print(f"Human implicature rate: {summary.get('human_implicature_rate', 0):.3f}")
        print(f"Processing success rate: {summary.get('model_processing_success_rate', 0):.3f}")
        
        # Show key metrics
        if 'metrics' in metrics:
            print("\nKEY METRICS:")
            for metric_name, metric_data in metrics['metrics'].items():
                value = metric_data.get('value', 'N/A')
                print(f"{metric_name}: {value}")
        
        print(f"\nResults saved to: {results_file}")
        print("Basic experiment completed successfully!")
        
    except Exception as e:
        logger.error(f"Basic experiment failed: {str(e)}")
        print(f"Error: {str(e)}")
        return False
    
    finally:
        # Cleanup
        if hasattr(runner, 'model_interface') and runner.model_interface:
            runner.model_interface.cleanup()
    
    return True

def run_quick_test():
    """Run a quick test with minimal scenarios."""
    print("Running quick test...")
    
    exp_config = get_experiment_config({
        'experiment_name': 'basic_quick_test',
        'total_scenarios': 5,
        'output_directory': 'results/quick_test'
    })
    
    runner = ExperimentRunner(exp_config)
    
    try:
        results = runner.run_quick_test(num_scenarios=5)
        
        print("QUICK TEST RESULTS:")
        print(f"Scenarios generated: {results['num_scenarios_generated']}")
        print(f"Model info: {results['model_info']['status']}")
        
        for i, test_result in enumerate(results['test_results']):
            print(f"\nTest {i+1}:")
            print(f"  Sentence: {test_result['target_sentence']}")
            print(f"  Implicature derived: {test_result['implicature_derived']}")
            print(f"  Processing success: {test_result['processing_success']}")
            print(f"  Response: {test_result['raw_response'][:100]}...")
        
        return True
        
    except Exception as e:
        print(f"Quick test failed: {str(e)}")
        return False
    
    finally:
        if hasattr(runner, 'model_interface') and runner.model_interface:
            runner.model_interface.cleanup()

if __name__ == "__main__":
    # Setup logging
    logging.basicConfig(level=logging.INFO)
    
    # Check command line arguments
    if len(sys.argv) > 1 and sys.argv[1] == "--quick":
        success = run_quick_test()
    else:
        success = run_basic_experiment()
    
    sys.exit(0 if success else 1)
