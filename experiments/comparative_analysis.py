#!/usr/bin/env python3
"""
Comparative analysis experiment.
Comprehensive comparison of model behavior to human patterns across all conditions.
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

import logging
import json
from typing import List, Dict, Any
from config.experiment_config import get_experiment_config
from src.experiment_runner import ExperimentRunner
from src.prompt_templates import PromptType
from src.analysis import ExperimentAnalyzer
import matplotlib.pyplot as plt
import seaborn as sns
import pandas as pd
import numpy as np

logger = logging.getLogger(__name__)

def run_comprehensive_experiment():
    """Run a comprehensive experiment covering all conditions."""
    print("Starting Comprehensive Scalar Implicature Experiment")
    print("=" * 55)
    
    # Configure comprehensive experiment
    exp_config = get_experiment_config({
        'experiment_name': 'comprehensive_scalar_implicature',
        'total_scenarios': 100,
        'samples_per_condition': 20,
        'test_quantifier_scales': True,
        'test_adjectival_scales': True,
        'test_negation_effects': True,
        'test_context_variations': True,
        'num_repetitions': 2,  # Run each scenario twice for reliability
        'output_directory': 'results/comprehensive'
    })
    
    # Create experiment runner
    runner = ExperimentRunner(exp_config)
    
    try:
        # Run complete experiment
        print("Running comprehensive experiment...")
        results_file = runner.run_complete_experiment(PromptType.INFERENCE)
        
        print(f"Experiment completed. Results saved to: {results_file}")
        return results_file
        
    except Exception as e:
        logger.error(f"Comprehensive experiment failed: {str(e)}")
        print(f"Error: {str(e)}")
        return None
    
    finally:
        # Cleanup
        if hasattr(runner, 'model_interface') and runner.model_interface:
            runner.model_interface.cleanup()

def create_comprehensive_analysis(results_file: str):
    """Create comprehensive analysis and visualizations."""
    print("Creating comprehensive analysis...")
    
    analyzer = ExperimentAnalyzer(results_file)
    
    # Generate all standard plots
    plot_files = analyzer.create_visualizations("results/comprehensive/plots")
    print(f"Created {len(plot_files)} standard plots")
    
    # Create additional comparative plots
    additional_plots = create_additional_visualizations(analyzer, "results/comprehensive/plots")
    print(f"Created {len(additional_plots)} additional plots")
    
    # Generate comprehensive report
    report_file = analyzer.generate_report("results/comprehensive/comprehensive_analysis_report.txt")
    
    # Add detailed comparative analysis
    add_detailed_analysis(analyzer, report_file)
    
    print(f"Comprehensive analysis saved to: {report_file}")
    
    # Print key findings
    print_key_findings(analyzer)

def create_additional_visualizations(analyzer: ExperimentAnalyzer, output_dir: str) -> List[str]:
    """Create additional visualization plots for comprehensive analysis."""
    os.makedirs(output_dir, exist_ok=True)
    plot_files = []
    
    if analyzer.model_df.empty:
        return plot_files
    
    # 1. Model vs Human correlation plot
    if not analyzer.human_df.empty:
        fig, ax = plt.subplots(figsize=(10, 8))
        
        # Calculate rates by condition
        model_rates = analyzer.model_df.groupby(['scale_type', 'context_type'])['implicature_derived'].mean()
        human_rates = analyzer.human_df.groupby(['scale_type', 'context_type'])['implicature_derived'].mean()
        
        # Find common conditions
        common_conditions = model_rates.index.intersection(human_rates.index)
        if len(common_conditions) > 0:
            model_vals = [model_rates[cond] for cond in common_conditions]
            human_vals = [human_rates[cond] for cond in common_conditions]
            
            ax.scatter(human_vals, model_vals, alpha=0.7, s=100)
            
            # Add diagonal line
            min_val = min(min(model_vals), min(human_vals))
            max_val = max(max(model_vals), max(human_vals))
            ax.plot([min_val, max_val], [min_val, max_val], 'r--', alpha=0.5)
            
            ax.set_xlabel('Human Implicature Rate')
            ax.set_ylabel('Model Implicature Rate')
            ax.set_title('Model vs Human Implicature Rates by Condition')
            
            # Add condition labels
            for i, cond in enumerate(common_conditions):
                ax.annotate(f"{cond[0][:4]}-{cond[1][:4]}", 
                           (human_vals[i], model_vals[i]), 
                           xytext=(5, 5), textcoords='offset points', fontsize=8)
            
            plot_file = os.path.join(output_dir, 'model_human_correlation.png')
            plt.savefig(plot_file, dpi=300, bbox_inches='tight')
            plt.close()
            plot_files.append(plot_file)
    
    # 2. Processing success by condition
    if 'processing_success' in analyzer.model_df.columns:
        fig, ax = plt.subplots(figsize=(12, 6))
        
        success_by_condition = analyzer.model_df.groupby(['scale_type', 'context_type'])['processing_success'].mean()
        
        # Create bar plot
        x_labels = [f"{st}-{ct}" for st, ct in success_by_condition.index]
        y_values = success_by_condition.values
        
        bars = ax.bar(range(len(x_labels)), y_values, alpha=0.7)
        ax.set_xlabel('Condition (Scale Type - Context Type)')
        ax.set_ylabel('Processing Success Rate')
        ax.set_title('Model Processing Success by Condition')
        ax.set_xticks(range(len(x_labels)))
        ax.set_xticklabels(x_labels, rotation=45, ha='right')
        ax.set_ylim(0, 1)
        
        # Add value labels on bars
        for bar, value in zip(bars, y_values):
            ax.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 0.01,
                   f'{value:.2f}', ha='center', va='bottom', fontsize=8)
        
        plot_file = os.path.join(output_dir, 'processing_success_by_condition.png')
        plt.savefig(plot_file, dpi=300, bbox_inches='tight')
        plt.close()
        plot_files.append(plot_file)
    
    # 3. Confidence distribution
    if 'confidence_score' in analyzer.model_df.columns:
        fig, ax = plt.subplots(figsize=(10, 6))
        
        confidence_data = analyzer.model_df['confidence_score'].dropna()
        if not confidence_data.empty:
            ax.hist(confidence_data, bins=20, alpha=0.7, edgecolor='black')
            ax.set_xlabel('Confidence Score')
            ax.set_ylabel('Frequency')
            ax.set_title('Distribution of Model Confidence Scores')
            ax.axvline(confidence_data.mean(), color='red', linestyle='--', 
                      label=f'Mean: {confidence_data.mean():.3f}')
            ax.legend()
            
            plot_file = os.path.join(output_dir, 'confidence_distribution.png')
            plt.savefig(plot_file, dpi=300, bbox_inches='tight')
            plt.close()
            plot_files.append(plot_file)
    
    return plot_files

def add_detailed_analysis(analyzer: ExperimentAnalyzer, report_file: str):
    """Add detailed comparative analysis to the report."""
    
    with open(report_file, 'a') as f:
        f.write("\n\nDETAILED COMPARATIVE ANALYSIS\n")
        f.write("=" * 40 + "\n")
        
        # Model-Human comparison by conditions
        if not analyzer.human_df.empty:
            comparison = analyzer.compare_model_human()
            
            f.write("\nMODEL-HUMAN COMPARISON BY CONDITIONS:\n")
            f.write("-" * 40 + "\n")
            
            for condition_type in ['by_scale_type', 'by_context_type', 'by_negation_type']:
                if condition_type in comparison:
                    f.write(f"\n{condition_type.replace('by_', '').replace('_', ' ').title()}:\n")
                    for condition, data in comparison[condition_type].items():
                        f.write(f"  {condition}:\n")
                        f.write(f"    Model: {data['model']:.3f}\n")
                        f.write(f"    Human: {data['human']:.3f}\n")
                        f.write(f"    Difference: {data['difference']:.3f}\n")
        
        # Processing statistics
        if not analyzer.model_df.empty:
            f.write("\nMODEL PROCESSING STATISTICS:\n")
            f.write("-" * 30 + "\n")
            
            total_responses = len(analyzer.model_df)
            successful_processing = analyzer.model_df['processing_success'].sum()
            valid_implicature = analyzer.model_df['implicature_derived'].notna().sum()
            
            f.write(f"Total responses: {total_responses}\n")
            f.write(f"Successful processing: {successful_processing} ({successful_processing/total_responses:.1%})\n")
            f.write(f"Valid implicature judgments: {valid_implicature} ({valid_implicature/total_responses:.1%})\n")
            
            if 'confidence_score' in analyzer.model_df.columns:
                confidence_data = analyzer.model_df['confidence_score'].dropna()
                if not confidence_data.empty:
                    f.write(f"Mean confidence: {confidence_data.mean():.3f}\n")
                    f.write(f"Confidence std: {confidence_data.std():.3f}\n")

def print_key_findings(analyzer: ExperimentAnalyzer):
    """Print key findings from the comprehensive analysis."""
    print("\nKEY FINDINGS:")
    print("=" * 20)
    
    summary = analyzer.get_summary_statistics()
    
    # Overall performance
    print(f"1. Overall model implicature rate: {summary.get('model_implicature_rate', 0):.3f}")
    print(f"   Overall human implicature rate: {summary.get('human_implicature_rate', 0):.3f}")
    
    # Processing success
    print(f"2. Model processing success rate: {summary.get('model_processing_success_rate', 0):.3f}")
    
    # Scale type effects
    if not analyzer.model_df.empty and 'scale_type' in analyzer.model_df.columns:
        scale_rates = analyzer.model_df.groupby('scale_type')['implicature_derived'].mean()
        print("3. Implicature rates by scale type:")
        for scale_type, rate in scale_rates.items():
            print(f"   {scale_type}: {rate:.3f}")
    
    # Context effects
    if not analyzer.model_df.empty and 'context_type' in analyzer.model_df.columns:
        context_rates = analyzer.model_df.groupby('context_type')['implicature_derived'].mean()
        print("4. Implicature rates by context type:")
        for context_type, rate in context_rates.items():
            print(f"   {context_type}: {rate:.3f}")
    
    # Model-human comparison
    if not analyzer.human_df.empty:
        comparison = analyzer.compare_model_human()
        overall_diff = comparison['overall']['difference']
        print(f"5. Model-human difference: {overall_diff:.3f}")
        if overall_diff < 0.1:
            print("   → Very close to human patterns")
        elif overall_diff < 0.2:
            print("   → Reasonably close to human patterns")
        else:
            print("   → Substantial difference from human patterns")

def run_comparative_analysis():
    """Run the complete comparative analysis."""
    print("Starting Comparative Analysis")
    print("=" * 30)
    
    # Run comprehensive experiment
    results_file = run_comprehensive_experiment()
    
    if results_file:
        # Create comprehensive analysis
        create_comprehensive_analysis(results_file)
        
        print("\nComparative analysis completed successfully!")
        print(f"All results and analysis saved in: results/comprehensive/")
        return True
    else:
        print("Comparative analysis failed!")
        return False

if __name__ == "__main__":
    # Setup logging
    logging.basicConfig(level=logging.INFO)
    
    success = run_comparative_analysis()
    sys.exit(0 if success else 1)
