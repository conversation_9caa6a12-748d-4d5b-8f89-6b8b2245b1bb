#!/usr/bin/env python3
"""
Negation effects experiment.
Tests how negation affects scalar implicature derivation.
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

import logging
from typing import List, Dict
from config.experiment_config import get_experiment_config
from data.scenarios import ScenarioGenerator, ScalarScenario, ScaleType, ContextType, NegationType
from src.experiment_runner import ExperimentRunner
from src.prompt_templates import PromptType
from src.analysis import ExperimentAnalyzer

logger = logging.getLogger(__name__)

def create_negation_scenarios() -> List[ScalarScenario]:
    """Create scenarios focused on negation effects."""
    generator = ScenarioGenerator()
    
    # Get representative scales from each type
    quantifier_scales = generator.get_scales_by_type(ScaleType.QUANTIFIER)[:2]
    adjectival_scales = generator.get_scales_by_type(ScaleType.ADJECTIVAL)[:3]
    
    scenarios = []
    
    # For each scale, test all negation types in simple context
    for scale in quantifier_scales + adjectival_scales:
        # No negation (baseline)
        scenarios.append(generator.generate_scenario(scale, ContextType.SIMPLE, NegationType.NONE))
        
        # Sentential negation
        scenarios.append(generator.generate_scenario(scale, ContextType.SIMPLE, NegationType.SENTENTIAL))
        
        # Constituent negation
        scenarios.append(generator.generate_scenario(scale, ContextType.SIMPLE, NegationType.CONSTITUENT))
    
    # Test negation in embedded contexts for key scales
    key_scales = quantifier_scales[:1] + adjectival_scales[:1]
    for scale in key_scales:
        scenarios.append(generator.generate_scenario(scale, ContextType.EMBEDDED, NegationType.SENTENTIAL))
        scenarios.append(generator.generate_scenario(scale, ContextType.EMBEDDED, NegationType.CONSTITUENT))
    
    return scenarios

def analyze_negation_effects(analyzer: ExperimentAnalyzer) -> Dict[str, Dict]:
    """Analyze the effects of different negation types."""
    if analyzer.model_df.empty:
        return {}
    
    negation_analysis = {}
    
    # Group by negation type
    for negation_type in ['none', 'sentential', 'constituent']:
        negation_data = analyzer.model_df[analyzer.model_df['negation_type'] == negation_type]
        
        if not negation_data.empty:
            negation_analysis[negation_type] = {
                'count': len(negation_data),
                'implicature_rate': negation_data['implicature_derived'].mean(),
                'processing_success': negation_data['processing_success'].mean(),
                'confidence_score': negation_data['confidence_score'].mean()
            }
    
    # Calculate negation effects (difference from baseline)
    if 'none' in negation_analysis:
        baseline_rate = negation_analysis['none']['implicature_rate']
        
        for negation_type in ['sentential', 'constituent']:
            if negation_type in negation_analysis:
                effect = negation_analysis[negation_type]['implicature_rate'] - baseline_rate
                negation_analysis[f'{negation_type}_effect'] = effect
    
    # Analyze by scale type and negation
    scale_negation_analysis = {}
    for scale_type in ['quantifier', 'adjectival']:
        scale_data = analyzer.model_df[analyzer.model_df['scale_type'] == scale_type]
        
        if not scale_data.empty:
            scale_negation_analysis[scale_type] = {}
            
            for negation_type in ['none', 'sentential', 'constituent']:
                subset = scale_data[scale_data['negation_type'] == negation_type]
                if not subset.empty:
                    scale_negation_analysis[scale_type][negation_type] = {
                        'count': len(subset),
                        'implicature_rate': subset['implicature_derived'].mean()
                    }
    
    negation_analysis['by_scale_type'] = scale_negation_analysis
    
    return negation_analysis

def run_negation_experiment():
    """Run the negation effects experiment."""
    print("Starting Negation Effects Experiment")
    print("=" * 40)
    
    # Configure experiment for negation testing
    exp_config = get_experiment_config({
        'experiment_name': 'negation_effects',
        'total_scenarios': 40,
        'samples_per_condition': 12,
        'test_quantifier_scales': True,
        'test_adjectival_scales': True,
        'test_negation_effects': True,
        'test_context_variations': True,
        'output_directory': 'results/negation_effects'
    })
    
    # Create experiment runner
    runner = ExperimentRunner(exp_config)
    
    try:
        # Generate negation scenarios
        print("Generating negation scenarios...")
        scenarios = create_negation_scenarios()
        print(f"Created {len(scenarios)} scenarios")
        
        # Show scenario breakdown
        negation_dist = {}
        scale_dist = {}
        for scenario in scenarios:
            neg_key = scenario.negation_type.value
            negation_dist[neg_key] = negation_dist.get(neg_key, 0) + 1
            
            scale_key = scenario.scale.scale_type.value
            scale_dist[scale_key] = scale_dist.get(scale_key, 0) + 1
        
        print(f"Negation distribution: {negation_dist}")
        print(f"Scale type distribution: {scale_dist}")
        
        # Run model experiment
        print("Running model experiment...")
        model_responses = runner.run_model_experiment(scenarios, PromptType.INFERENCE)
        
        # Simulate human responses
        print("Simulating human responses...")
        human_responses = runner.simulate_human_experiment(scenarios, num_participants=35)
        
        # Calculate metrics
        print("Calculating metrics...")
        metrics = runner.calculate_metrics(model_responses, scenarios, human_responses)
        
        # Save results
        print("Saving results...")
        results_file = runner.save_results(scenarios, model_responses, human_responses, metrics)
        
        # Generate analysis
        print("Generating analysis...")
        analyzer = ExperimentAnalyzer(results_file)
        
        # Negation-specific analysis
        negation_analysis = analyze_negation_effects(analyzer)
        print("\nNEGATION EFFECTS ANALYSIS:")
        for key, value in negation_analysis.items():
            if key != 'by_scale_type':
                print(f"{key}: {value}")
        
        # Create visualizations
        plot_files = analyzer.create_visualizations("results/negation_effects/plots")
        print(f"Created {len(plot_files)} plots")
        
        # Generate report
        report_file = analyzer.generate_report("results/negation_effects/negation_analysis_report.txt")
        
        # Add negation analysis to report
        with open(report_file, 'a') as f:
            f.write("\n\nNEGATION EFFECTS ANALYSIS\n")
            f.write("-" * 30 + "\n")
            
            # Overall negation effects
            f.write("\nOverall Effects:\n")
            for key, value in negation_analysis.items():
                if key not in ['by_scale_type'] and isinstance(value, dict):
                    f.write(f"{key}: {value}\n")
            
            # Effects by scale type
            if 'by_scale_type' in negation_analysis:
                f.write("\nEffects by Scale Type:\n")
                for scale_type, scale_data in negation_analysis['by_scale_type'].items():
                    f.write(f"\n{scale_type.upper()}:\n")
                    for neg_type, neg_data in scale_data.items():
                        f.write(f"  {neg_type}: {neg_data}\n")
        
        print(f"Analysis report saved to: {report_file}")
        
        # Print summary
        summary = analyzer.get_summary_statistics()
        print("\nEXPERIMENT SUMMARY:")
        print(f"Total scenarios: {summary.get('total_scenarios', 0)}")
        print(f"Model implicature rate: {summary.get('model_implicature_rate', 0):.3f}")
        print(f"Human implicature rate: {summary.get('human_implicature_rate', 0):.3f}")
        print(f"Processing success rate: {summary.get('model_processing_success_rate', 0):.3f}")
        
        # Show negation effects
        if 'sentential_effect' in negation_analysis:
            print(f"Sentential negation effect: {negation_analysis['sentential_effect']:.3f}")
        if 'constituent_effect' in negation_analysis:
            print(f"Constituent negation effect: {negation_analysis['constituent_effect']:.3f}")
        
        print(f"\nResults saved to: {results_file}")
        print("Negation effects experiment completed successfully!")
        
    except Exception as e:
        logger.error(f"Negation experiment failed: {str(e)}")
        print(f"Error: {str(e)}")
        return False
    
    finally:
        # Cleanup
        if hasattr(runner, 'model_interface') and runner.model_interface:
            runner.model_interface.cleanup()
    
    return True

if __name__ == "__main__":
    # Setup logging
    logging.basicConfig(level=logging.INFO)
    
    success = run_negation_experiment()
    sys.exit(0 if success else 1)
