#!/usr/bin/env python3
"""
Adjectival scales experiment.
Focuses specifically on adjectival scalar implicatures across different domains.
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

import logging
from typing import List, Dict
from config.experiment_config import get_experiment_config
from data.scenarios import ScenarioGenerator, ScalarScenario, ScaleType, ContextType, NegationType
from src.experiment_runner import ExperimentRunner
from src.prompt_templates import PromptType
from src.analysis import ExperimentAnalyzer

logger = logging.getLogger(__name__)

def create_adjectival_scenarios() -> List[ScalarScenario]:
    """Create scenarios focused on adjectival scales."""
    generator = ScenarioGenerator()
    
    # Get all adjectival scales
    adjectival_scales = generator.get_scales_by_type(ScaleType.ADJECTIVAL)
    
    scenarios = []
    
    # Test each adjectival scale in multiple contexts
    for scale in adjectival_scales:
        # Simple context
        scenarios.append(generator.generate_scenario(scale, ContextType.SIMPLE, NegationType.NONE))
        
        # Embedded context
        scenarios.append(generator.generate_scenario(scale, ContextType.EMBEDDED, NegationType.NONE))
        
        # Question context
        scenarios.append(generator.generate_scenario(scale, ContextType.QUESTION, NegationType.NONE))
    
    # Add some negation contexts for key scales
    key_scales = [scale for scale in adjectival_scales if scale.domain in ['temperature', 'size', 'quality']]
    for scale in key_scales:
        scenarios.append(generator.generate_scenario(scale, ContextType.SIMPLE, NegationType.CONSTITUENT))
    
    return scenarios

def analyze_by_domain(analyzer: ExperimentAnalyzer) -> Dict[str, Dict]:
    """Analyze results by adjectival domain."""
    if analyzer.model_df.empty:
        return {}
    
    # Group scenarios by domain (extracted from scale information)
    domain_analysis = {}
    
    # This is a simplified analysis - in a full implementation,
    # we would need to map scales to domains more systematically
    domain_keywords = {
        'temperature': ['warm', 'cool', 'hot', 'cold'],
        'size': ['large', 'small', 'huge', 'tiny'],
        'quality': ['good', 'bad', 'excellent', 'terrible'],
        'speed': ['fast', 'slow']
    }
    
    for domain, keywords in domain_keywords.items():
        domain_data = analyzer.model_df[
            analyzer.model_df['scale_weak'].isin(keywords) | 
            analyzer.model_df['scale_strong'].isin(keywords)
        ]
        
        if not domain_data.empty:
            domain_analysis[domain] = {
                'count': len(domain_data),
                'implicature_rate': domain_data['implicature_derived'].mean(),
                'processing_success': domain_data['processing_success'].mean(),
                'confidence_score': domain_data['confidence_score'].mean()
            }
    
    return domain_analysis

def run_adjectival_experiment():
    """Run the adjectival scales experiment."""
    print("Starting Adjectival Scales Experiment")
    print("=" * 40)
    
    # Configure experiment for adjectival testing
    exp_config = get_experiment_config({
        'experiment_name': 'adjectival_scales',
        'total_scenarios': 50,
        'samples_per_condition': 15,
        'test_quantifier_scales': False,  # Focus only on adjectival
        'test_adjectival_scales': True,
        'test_negation_effects': True,
        'test_context_variations': True,
        'output_directory': 'results/adjectival_scales'
    })
    
    # Create experiment runner
    runner = ExperimentRunner(exp_config)
    
    try:
        # Generate adjectival scenarios
        print("Generating adjectival scenarios...")
        scenarios = create_adjectival_scenarios()
        print(f"Created {len(scenarios)} scenarios")
        
        # Show scenario breakdown
        scale_types = {}
        context_types = {}
        for scenario in scenarios:
            scale_key = f"{scenario.scale.weak_term}-{scenario.scale.strong_term}"
            scale_types[scale_key] = scale_types.get(scale_key, 0) + 1
            context_types[scenario.context_type.value] = context_types.get(scenario.context_type.value, 0) + 1
        
        print(f"Scale distribution: {scale_types}")
        print(f"Context distribution: {context_types}")
        
        # Run model experiment with different prompt types
        print("Running model experiment with inference prompts...")
        model_responses_inference = runner.run_model_experiment(scenarios, PromptType.INFERENCE)
        
        print("Running model experiment with truth value prompts...")
        model_responses_truth = runner.run_model_experiment(scenarios[:10], PromptType.TRUTH_VALUE)
        
        # Combine responses (using inference as primary)
        model_responses = model_responses_inference
        
        # Simulate human responses
        print("Simulating human responses...")
        human_responses = runner.simulate_human_experiment(scenarios, num_participants=40)
        
        # Calculate metrics
        print("Calculating metrics...")
        metrics = runner.calculate_metrics(model_responses, scenarios, human_responses)
        
        # Save results
        print("Saving results...")
        results_file = runner.save_results(scenarios, model_responses, human_responses, metrics)
        
        # Generate analysis
        print("Generating analysis...")
        analyzer = ExperimentAnalyzer(results_file)
        
        # Domain-specific analysis
        domain_analysis = analyze_by_domain(analyzer)
        print("\nDOMAIN ANALYSIS:")
        for domain, stats in domain_analysis.items():
            print(f"{domain}: {stats}")
        
        # Create visualizations
        plot_files = analyzer.create_visualizations("results/adjectival_scales/plots")
        print(f"Created {len(plot_files)} plots")
        
        # Generate report
        report_file = analyzer.generate_report("results/adjectival_scales/adjectival_analysis_report.txt")
        
        # Add domain analysis to report
        with open(report_file, 'a') as f:
            f.write("\n\nDOMAIN-SPECIFIC ANALYSIS\n")
            f.write("-" * 30 + "\n")
            for domain, stats in domain_analysis.items():
                f.write(f"\n{domain.upper()}:\n")
                for key, value in stats.items():
                    f.write(f"  {key}: {value}\n")
        
        print(f"Analysis report saved to: {report_file}")
        
        # Print summary
        summary = analyzer.get_summary_statistics()
        print("\nEXPERIMENT SUMMARY:")
        print(f"Total scenarios: {summary.get('total_scenarios', 0)}")
        print(f"Model implicature rate: {summary.get('model_implicature_rate', 0):.3f}")
        print(f"Human implicature rate: {summary.get('human_implicature_rate', 0):.3f}")
        print(f"Processing success rate: {summary.get('model_processing_success_rate', 0):.3f}")
        
        # Compare with human patterns
        if not analyzer.human_df.empty:
            comparison = analyzer.compare_model_human()
            print(f"\nMODEL-HUMAN COMPARISON:")
            print(f"Overall difference: {comparison['overall']['difference']:.3f}")
            print(f"Model higher than human: {comparison['overall']['model_higher']}")
        
        print(f"\nResults saved to: {results_file}")
        print("Adjectival scales experiment completed successfully!")
        
    except Exception as e:
        logger.error(f"Adjectival experiment failed: {str(e)}")
        print(f"Error: {str(e)}")
        return False
    
    finally:
        # Cleanup
        if hasattr(runner, 'model_interface') and runner.model_interface:
            runner.model_interface.cleanup()
    
    return True

if __name__ == "__main__":
    # Setup logging
    logging.basicConfig(level=logging.INFO)
    
    success = run_adjectival_experiment()
    sys.exit(0 if success else 1)
